#include <iostream>
#include "ams_client.h"

using namespace amssdk;

int main() {
  AmsClient client("http://10.0.18.41/v1");

  if (!client.SetAuthorizationKey("app-HsG5fVJiIYdCLLYnIO2tzUf4")) {
    std::cerr << "Failed to set authorization key" << std::endl;
    return -1;
  }
  client.SetMaxTimeout(300000);

  std::string user = "abc-123";

  ConversationRequest request;
  request.SetUser(user);
  request.SetLastId("");
  request.SetLimit("10");
  request.SetSortRule(ConversationRequest::SortRule::kCreatedAt);

  auto result = client.GetConversation(request);
  if (result.ok) {
    std::cout << "Conversation retrieved successfully:" << std::endl;
    std::cout << "data: " << result.data.data << std::endl;
    std::cout << "limit: " << result.data.limit << std::endl;
    std::cout << "has_more: " << result.data.has_more << std::endl;
  } else {
    std::cerr << "Failed to get conversation: " << result.error.message
              << std::endl;
  }

  return 0;
}
