#include <iostream>
#include "../libams/include/knowledge/knowledge.h"
#include "ams_client.h"

using namespace amssdk;

void PrintError(const std::string& operation, const std::string& raw_body) {
  try {
    auto json = nlohmann::json::parse(raw_body);
    if (json.contains("message")) {
      std::cerr << operation
                << " failed: " << json["message"].get<std::string>()
                << std::endl;
    } else {
      std::cerr << operation << " failed: " << raw_body << std::endl;
    }
  } catch (const std::exception&) {
    std::cerr << operation << " failed: " << raw_body << std::endl;
  }
}

int main() {
  // 创建客户端
  AmsClient client("http://**********/v1");

  // 设置授权密钥
  if (!client.SetAuthorizationKey("dataset-CzGRgQ5UTRkaoZFMf1J427zR")) {
    std::cerr << "Failed to set authorization key" << std::endl;
    return -1;
  }

  client.SetMaxTimeout(300000);

  std::cout << "=== Knowledge Dataset Management Demo ===" << std::endl;

  // 1. 创建知识库
  std::cout << "\n1. Creating dataset..." << std::endl;
  CreateDatasetRequest create_dataset_req;
  create_dataset_req.SetName("Demo Dataset")
      .SetDescription("A demo dataset for testing")
      .SetIndexingTechnique(IndexingTechnique::kHighQuality)
      .SetPermission(CreateDatasetPermission::kOnlyMe)
      .SetProvider(CreateDatasetProvider::kVendor);

  auto create_dataset_result = client.CreateDataset(create_dataset_req);
  if (!create_dataset_result.ok) {
    PrintError("Create dataset", create_dataset_result.raw_body);
    return -1;
  }

  std::string dataset_id = create_dataset_result.data.GetDataset().id;
  std::cout << "Dataset created with ID: " << dataset_id << std::endl;

  // 2. 列出知识库
  std::cout << "\n2. Listing datasets..." << std::endl;
  ListDatasetsRequest list_datasets_req;
  list_datasets_req.SetPage(1).SetLimit(10);

  auto list_datasets_result = client.ListDatasets(list_datasets_req);
  if (!list_datasets_result.ok) {
    PrintError("List datasets", list_datasets_result.raw_body);
  } else {
    std::cout << "Found " << list_datasets_result.data.GetTotal() << " datasets"
              << std::endl;
  }

  // 3. 删除知识库
  std::cout << "\n3. Deleting dataset..." << std::endl;
  DeleteDatasetRequest delete_dataset_req;
  delete_dataset_req.SetDatasetId(dataset_id);
  auto delete_result = client.DeleteDataset(delete_dataset_req);
  if (!delete_result.ok) {
    PrintError("Delete dataset", delete_result.raw_body);
  } else {
    std::cout << "Dataset deleted successfully" << std::endl;
  }

  std::cout << "\n=== Dataset Demo completed ===" << std::endl;
  return 0;
}
