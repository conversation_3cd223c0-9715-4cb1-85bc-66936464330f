#include <iostream>
#include "ams_client.h"
#include "include/chat/chat_request.h"

using namespace amssdk;

int main() {
  AmsClient client("http://10.0.18.41/v1");

  if (!client.SetAuthorizationKey("app-HsG5fVJiIYdCLLYnIO2tzUf4")) {
    std::cerr << "Failed to set authorization key" << std::endl;
    return -1;
  }
  client.SetMaxTimeout(300000);

  // Example task ID to stop (replace with actual task ID)
  std::string taskId = "task-123456";
  std::string user = "abc-123";

  auto result = client.StopTask(taskId, user);

  if (result.ok) {
    std::cout << "Task stopped successfully: " << taskId << std::endl;
  } else {
    std::cerr << "Failed to stop task: " << result.error.message << std::endl;
  }

  return 0;
}
