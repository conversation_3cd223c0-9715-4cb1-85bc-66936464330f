#include <iostream>
#include "ams_client.h"

using namespace amssdk;

int main() {
  AmsClient client("http://10.0.18.41/v1");

  if (!client.SetAuthorizationKey("app-HsG5fVJiIYdCLLYnIO2tzUf4")) {
    std::cerr << "Failed to set authorization key" << std::endl;
    return -1;
  }
  client.SetMaxTimeout(300000);

  std::string user = "abc-123";
  SuggestedRequest request;
  request.SetUser(user);
  request.SetMessageId("7613ced9-a7a8-4866-9022-510e74b01de2");

  auto result = client.GetSuggested(request);
  if (result.ok) {
    std::cout << "Suggested questions retrieved successfully:" << std::endl;
    std::cout << "data: " << result.data.data << std::endl;
  } else {
    std::cerr << "Failed to get suggested questions: " << result.error.message
              << std::endl;
  }

  return 0;
}
