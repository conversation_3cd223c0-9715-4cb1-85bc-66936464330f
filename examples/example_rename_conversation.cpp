#include <iostream>
#include "ams_client.h"

using namespace amssdk;

int main() {
  AmsClient client("http://10.0.18.41/v1");

  if (!client.SetAuthorizationKey("app-HsG5fVJiIYdCLLYnIO2tzUf4")) {
    std::cerr << "Failed to set authorization key" << std::endl;
    return -1;
  }
  client.SetMaxTimeout(300000);

  std::string conversationId = "9baaf11a-32bb-4648-98f1-fc08e7938f70";
  std::string new_name = "New Conversation Name";
  std::string user = "abc-123";

  RenameConversationRequest request;
  request.SetConversationId(conversationId);
  request.SetName(new_name);
  request.SetUser(user);
  request.SetAutoGenerateName(false);

  auto result = client.RenameConversation(request);

  if (result.ok && result.success) {
    std::cout << "Conversation renamed successfully: " << conversationId
              << std::endl;
    std::cout << "New name: " << new_name << std::endl;
  } else {
    std::cerr << "Failed to rename conversation: " << result.error.message
              << std::endl;
  }

  return 0;
}
