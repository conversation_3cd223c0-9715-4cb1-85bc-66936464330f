#include <iostream>
#include "ams_client.h"

using namespace amssdk;

int main() {
  AmsClient client("http://10.0.18.41/v1");

  if (!client.SetAuthorizationKey("app-HsG5fVJiIYdCLLYnIO2tzUf4")) {
    std::cerr << "Failed to set authorization key" << std::endl;
    return -1;
  }
  client.SetMaxTimeout(300000);

  std::string conversationId = "9baaf11a-32bb-4648-98f1-fc08e7938f70";
  std::string user = "abc-123";

  MessagesRequest request;
  request.SetConversationId(conversationId);
  request.SetUser(user);
  request.SetFirstId("");
  request.SetLimit(10);

  auto result = client.GetMessages(request);
  if (result.ok) {
    std::cout << "Messages retrieved successfully for conversation: "
              << conversationId << std::endl;
    std::cout << "data: " << result.data.data << std::endl;
  } else {
    std::cerr << "Failed to get messages: " << result.error.message
              << std::endl;
  }

  return 0;
}
