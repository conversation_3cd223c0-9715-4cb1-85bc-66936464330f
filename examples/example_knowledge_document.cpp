#include <experimental/filesystem>
#include <iostream>
#include "../libams/include/knowledge/knowledge.h"
#include "ams_client.h"

using namespace amssdk;

void PrintError(const std::string& operation, const std::string& raw_body) {
  try {
    auto json = nlohmann::json::parse(raw_body);
    if (json.contains("message")) {
      std::cerr << operation
                << " failed: " << json["message"].get<std::string>()
                << std::endl;
    } else {
      std::cerr << operation << " failed: " << raw_body << std::endl;
    }
  } catch (const std::exception&) {
    std::cerr << operation << " failed: " << raw_body << std::endl;
  }
}

int main() {
  // 创建客户端
  AmsClient client("http://10.0.18.41/v1");

  // 设置授权密钥
  if (!client.SetAuthorizationKey("dataset-CzGRgQ5UTRkaoZFMf1J427zR")) {
    std::cerr << "Failed to set authorization key" << std::endl;
    return -1;
  }

  client.SetMaxTimeout(300000);

  // 使用已存在的数据集ID
  std::string dataset_id = "1e41b175-5180-4181-8c70-03adbecf2229";
  std::cout << "=== Knowledge Document Management Demo ===" << std::endl;

  // 1. 通过文本创建文档
  std::cout << "\n1. Creating document by text..." << std::endl;
  CreateDocumentByTextRequest create_text_req;
  create_text_req.SetName("Text Document")
      .SetText(
          "This is a sample document created from text content. It contains "
          "information about knowledge management.")
      .SetDatasetId(dataset_id);

  auto create_text_result = client.CreateDocumentByText(create_text_req);
  if (!create_text_result.ok) {
    PrintError("Create document by text", create_text_result.raw_body);
  } else {
    std::string document_id = create_text_result.data.GetDocument();
    std::string batch_id = create_text_result.data.GetBatch();
    std::cout << "Text document created with ID: " << document_id << std::endl;
    std::cout << "Batch ID: " << batch_id << std::endl;
  }

  // 2. 通过文件创建文档
  std::cout << "\n2. Creating document by file..." << std::endl;
  CreateDocumentByFileRequest create_file_req;
  create_file_req.SetDatasetId(dataset_id)
      .SetFile(std::experimental::filesystem::path(
          R"(C:\Users\<USER>\Downloads\基于改进免疫遗传算法的汽车零件排样.pdf)"))
      .SetIndexingTechnique(IndexingTechnique::kHighQuality);

  ProcessRule process_rule;
  process_rule.mode = ProcessRuleMode::AUTOMATIC;
  create_file_req.SetProcessRule(process_rule);
  auto create_file_result = client.CreateDocumentByFile(create_file_req);
  if (!create_file_result.ok) {
    PrintError("Create document by file", create_file_result.raw_body);
  } else {
    std::cout << "File document created successfully" << std::endl;
  }

  // 3. 更新文档
  std::cout << "\n3. Updating document by text..." << std::endl;
  UpdateDocumentByTextRequest update_doc_req;
  update_doc_req.SetDatasetId(dataset_id)
      .SetDocumentId("c10b15ed-6c6d-4fd6-bb05-ae6498176975")
      .SetText(
          "Updated content: This document has been modified with new "
          "information.")
      .SetName("Updated Text Document");

  auto update_doc_result = client.UpdateDocumentByText(update_doc_req);
  if (!update_doc_result.ok) {
    PrintError("Update document by text", update_doc_result.raw_body);
  } else {
    std::cout << "Document updated successfully" << std::endl;
  }

  // 4. 通过文件更新文档。
  std::cout << "\n4. Updating document by File..." << std::endl;
  UpdateDocumentByFileRequest update_doc_req_file;

  update_doc_req_file.SetDatasetId(dataset_id)
      .SetDocumentId("da38fce0-29c0-4ce1-bb18-1435a6a4fd3c")
      .SetFile(R"(C:\Users\<USER>\Downloads\123.pdf)")
      .SetName("Updated Text Document By File 123");
  auto update_doc_result_file =
      client.UpdateDocumentByFile(update_doc_req_file);

  if (update_doc_result_file.ok) {
    std::cout << "Document updated successfully" << std::endl;
  } else {
    PrintError("Update document by file", update_doc_result_file.raw_body);
  }

  // 5. 列出文档
  std::cout << "\n5. Listing documents..." << std::endl;
  ListDocumentsRequest list_docs_req;
  list_docs_req.SetDatasetId(dataset_id).SetPage(1).SetLimit(10);

  auto list_docs_result = client.ListDocuments(list_docs_req);
  if (!list_docs_result.ok) {
    PrintError("List documents", list_docs_result.raw_body);
  } else {
    std::cout << "Found " << list_docs_result.data.GetTotal() << " documents"
              << std::endl;
  }

  DeleteDocumentRequest delete_doc_req;
  delete_doc_req.SetDatasetId(dataset_id)
      .SetDocumentId("c10b15ed-6c6d-4fd6-bb05-ae6498176975");

  auto delete_doc_result = client.DeleteDocument(delete_doc_req);
  if (!delete_doc_result.ok) {
    PrintError("Delete document", delete_doc_result.raw_body);
  } else {
    std::cout << "Document deleted successfully" << std::endl;
  }
  std::cout << "\n=== Document Demo completed ===" << std::endl;
  return 0;
}
