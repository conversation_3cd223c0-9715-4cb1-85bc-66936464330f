#include <iostream>

#include "ams_client.h"
#include "include/workflow.h"
using namespace amssdk;

int main() {
  AmsClient client("http://10.0.18.41/v1");

  WorkflowRunRequest request;
  request.SetUser("abc-123");
  request.SetResponseMode(ResponseMode::BLOCKING);
  request.SetInputs({{"query", "成都最近7天的天气预报？"}});

  if (!client.SetAuthorizationKey("app-AH8NRZNiWATarWbMl4zDtvo3")) {
    return -1;
  }
  client.SetMaxTimeout(300000);

  auto result = client.WorkflowRun(
      request, [](const std::unique_ptr<StreamEvent>& event) {
        auto event_type = event->type();
        if (event_type == StreamEvent::Type::MESSAGE) {
          const auto* message_event = event->as<MessageEvent>();
          std::cout << message_event->Answer();
        } else if (event_type == StreamEvent::Type::AGENT_THOUGHT) {
          const auto* message_event = event->as<AgentThoughtEvent>();
          std::cout << "Thought Message: " << message_event->Thought()
                    << std::endl;
        } else if (event_type == StreamEvent::Type::AGENT_MESSAGE) {
          const auto* message_event = event->as<MessageEvent>();
          std::cout << "Agent Message: " << message_event->Answer()
                    << std::endl;
        } else if (event_type == StreamEvent::Type::MESSAGE_END) {
          const auto* message_event = event->as<MessageEndEvent>();
          std::cout << "Message End"
                    << message_event->as<MessageEndEvent>()->ConversationId()
                    << std::endl;
        } else if (event_type == StreamEvent::Type::ERR) {
          const auto* message_event = event->as<ErrorEvent>();
          std::cout << "Error: " << message_event->Message() << std::endl;
        } else if (event_type == StreamEvent::Type::WORKFLOW_STARTED ||
                   event_type == StreamEvent::Type::NODE_FINISHED ||
                   event_type == StreamEvent::Type::WORKFLOW_FINISHED ||
                   event_type == StreamEvent::Type::NODE_STARTED) {
          const auto* message_event = event->as<WorkflowEvent>();
          std::cout << "Workflow Event: " << message_event->GetDataJson()
                    << std::endl;

        } else {
          std::cout << "Unknown event type" << std::endl;
        }
      });
  if (!result.ok) {
    std::cerr << "request failed: " << result.error.message << std::endl;
  } else {
    std::cout << "Workflow Run Id: " << result.data.GetWorkflowRunId()
              << std::endl;
  }
  return 0;
}
