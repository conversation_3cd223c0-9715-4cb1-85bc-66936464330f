#include <iostream>
#include "ams_client.h"

using namespace amssdk;

int main() {
  AmsClient client("http://10.0.18.41/v1");

  if (!client.SetAuthorizationKey("app-HsG5fVJiIYdCLLYnIO2tzUf4")) {
    std::cerr << "Failed to set authorization key" << std::endl;
    return -1;
  }
  client.SetMaxTimeout(300000);

  DeleteConversationRequest request;
  request.SetConversationId("9baaf11a-32bb-4648-98f1-fc08e7938f70");
  request.SetUser("abc-123");

  auto result = client.DeleteConversation(request);
  if (result.ok && result.success) {
    std::cout << "Conversation deleted successfully: "
              << request.GetConversationId() << std::endl;
  } else {
    std::cerr << "Failed to delete conversation: " << result.error.message
              << std::endl;
  }

  return 0;
}
