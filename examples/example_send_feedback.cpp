#include <iostream>
#include "ams_client.h"

using namespace amssdk;

int main() {
  AmsClient client("http://10.0.18.41/v1");

  if (!client.SetAuthorizationKey("app-HsG5fVJiIYdCLLYnIO2tzUf4")) {
    std::cerr << "Failed to set authorization key" << std::endl;
    return -1;
  }
  client.SetMaxTimeout(300000);

  std::string messageId = "message-123456";
  std::string rating = "like";  // or "dislike"
  std::string content = "This response was very helpful!";
  std::string user = "abc-123";

  FeedbackRequest request;
  request.SetUser(user);
  request.SetMessageId(messageId);
  request.SetRating(FeedbackRequest::Rating::kLike);
  request.SetContent(content);

  auto result = client.SendFeedback(request);
  if (result.ok) {
    std::cout << "Feedback sent successfully for message: " << messageId
              << std::endl;
    std::cout << "Rating: " << rating << std::endl;
    std::cout << "Content: " << content << std::endl;
  } else {
    std::cerr << "Failed to send feedback: " << result.error.message
              << std::endl;
  }

  return 0;
}
