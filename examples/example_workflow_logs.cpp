#include <iostream>

#include "ams_client.h"
#include "include/workflow.h"
using namespace amssdk;

int main() {
  AmsClient client("http://10.0.18.41/v1");

  WorkflowLogsRequest request;
  request.SetKeyWord("test");
  request.SetStatus("succeeded");
  request.SetPage(1);
  request.SetLimit(10);

  if (!client.SetAuthorizationKey("app-AH8NRZNiWATarWbMl4zDtvo3")) {
    return -1;
  }
  client.SetMaxTimeout(300000);

  auto result = client.WorkflowLogs(request);
  if (!result.ok) {
    // 尝试解析错误信息，如果解析失败则输出原始信息
    try {
      auto json = nlohmann::json::parse(result.raw_body);
      if (json.contains("message")) {
        std::cerr << "request failed: " << json["message"].get<std::string>()
                  << std::endl;
      } else {
        std::cerr << "request failed: " << result.raw_body << std::endl;
      }
    } catch (const std::exception&) {
      std::cerr << "request failed: " << result.raw_body << std::endl;
    }
  } else {
    std::cout << "Workflow logs success: " << result.data.GetData()
              << std::endl;
  }
  return 0;
}