#include <iostream>

#include "ams_client.h"
#include "include/app.h"

using namespace amssdk;

int main() {
  AmsClient client("http://10.0.18.41/v1");

  if (!client.SetAuthorizationKey("app-HsG5fVJiIYdCLLYnIO2tzUf4")) {
    std::cerr << "Failed to set authorization key" << std::endl;
    return -1;
  }
  client.SetMaxTimeout(300000);

  std::string user = "abc-123";
  AppInfoRequest request;
  request.SetUser(user);

  auto result = client.AppInfo(request);

  if (result.ok) {
    std::cout << "App meta retrieved successfully:" << std::endl;
    std::cout << "data: " << result.raw_body << std::endl;
  } else {
    std::cerr << "Failed to get app meta: " << result.error.message
              << std::endl;
  }

  return 0;
}
