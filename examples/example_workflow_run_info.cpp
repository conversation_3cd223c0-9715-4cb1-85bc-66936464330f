#include <iostream>

#include "ams_client.h"
#include "include/workflow.h"
using namespace amssdk;

int main() {
  AmsClient client("http://10.0.18.41/v1");

  WorkflowRunInfoRequest request;
  request.SetWorkflowRunId("df3ec3f9-a9ee-44fc-bcd4-f78fe407c20d");

  if (!client.SetAuthorizationKey("app-AH8NRZNiWATarWbMl4zDtvo3")) {
    return -1;
  }
  client.SetMaxTimeout(300000);

  auto result = client.WorkflowRunInfo(request);
  if (!result.ok) {
    std::cerr << "request failed: " << result.error.message << std::endl;
  } else {
    std::cout << "Workflow Run Id: " << result.data.GetId() << std::endl;
  }
  return 0;
}