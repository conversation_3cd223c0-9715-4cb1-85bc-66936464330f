#include <iostream>
#include "ams_client.h"

int main() {

  amssdk::AmsClient client("http://10.0.18.41/v1");
  if (!client.SetAuthorizationKey("app-HsG5fVJiIYdCLLYnIO2tzUf4")) {
    return -1;
  }
  client.SetMaxTimeout(300000);

  amssdk::FileRequest request;
  request.SetFilePath(R"(D:\Workspace\AmsSdk\examples\test_data\cat .png)");
  request.SetUser("abc-123");
  request.SetFileType(amssdk::FileAttachment::FileType::IMAGE);
  auto result = client.FileUpload(request);
  std::cout << "File id: " << result.data.id_ << std::endl;
  if (!result.ok) {
    std::cerr << "request failed: " << result.error.reason << std::endl;
  }
}