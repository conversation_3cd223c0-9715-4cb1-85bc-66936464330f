#include <iostream>
#include "../libams/include/knowledge/knowledge_segments.h"
#include "ams_client.h"

using namespace amssdk;

void PrintError(const std::string& operation, const std::string& raw_body) {
  try {
    auto json = nlohmann::json::parse(raw_body);
    if (json.contains("message")) {
      std::cerr << operation
                << " failed: " << json["message"].get<std::string>()
                << std::endl;
    } else {
      std::cerr << operation << " failed: " << raw_body << std::endl;
    }
  } catch (const std::exception&) {
    std::cerr << operation << " failed: " << raw_body << std::endl;
  }
}

int main() {
  // 创建客户端
  AmsClient client("http://10.0.18.41/v1");

  // 设置授权密钥
  if (!client.SetAuthorizationKey("dataset-CzGRgQ5UTRkaoZFMf1J427zR")) {
    std::cerr << "Failed to set authorization key" << std::endl;
    return -1;
  }

  client.SetMaxTimeout(300000);

  // 使用已存在的数据集和文档ID
  std::string dataset_id = "1e41b175-5180-4181-8c70-03adbecf2229";
  std::string document_id = "0271c5b1-9403-4c9f-8e00-19dbb5bebc88";

  std::cout << "=== Knowledge Segment Management Demo ===" << std::endl;

  // 1. 创建分段
  std::cout << "\n1. Creating segments..." << std::endl;
  CreateSegmentRequest create_seg_req;
  create_seg_req.SetDatasetId(dataset_id).SetDocumentId(document_id);

  std::vector<SegmentInfo> segments;

  // 第一个分段
  SegmentInfo segment1;
  segment1.content = "这是第一个分段的内容，包含关于人工智能的基础知识。";
  segment1.answer = "人工智能是模拟人类智能的技术。";
  segment1.keywords = {"人工智能", "AI", "技术"};
  segments.push_back(segment1);

  // 第二个分段
  SegmentInfo segment2;
  segment2.content = "这是第二个分段的内容，讲述机器学习的应用场景。";
  segment2.answer = "机器学习广泛应用于数据分析、预测和自动化。";
  segment2.keywords = {"机器学习", "应用", "数据分析"};
  segments.push_back(segment2);

  create_seg_req.SetSegments(segments);

  auto create_seg_result = client.CreateSegment(create_seg_req);
  if (!create_seg_result.ok) {
    PrintError("Create segments", create_seg_result.raw_body);
  } else {
    std::cout << "Segments created successfully" << std::endl;
  }

  // 2. 列出分段
  std::cout << "\n2. Listing segments..." << std::endl;
  ListSegmentsRequest list_segs_req;
  list_segs_req.SetDatasetId(dataset_id).SetDocumentId(document_id);

  auto segs_result = client.ListSegments(list_segs_req);
  if (!segs_result.ok) {
    PrintError("List segments", segs_result.raw_body);
  } else {
    const auto& segment_data = segs_result.data.GetData();
    std::cout << "Found " << segment_data << std::endl;
  }

  std::cout << "\n 3.Delete segments..." << std::endl;

  DeleteSegmentRequest delete_seg_req;
  delete_seg_req.SetDatasetId(dataset_id)
      .SetDocumentId(document_id)
      .SetSegmentId("852a5845-872b-4eae-aee4-24cc80074aaa");
  auto delete_seg_result = client.DeleteSegment(delete_seg_req);
  if (!delete_seg_result.ok) {
    PrintError("Delete segments", delete_seg_result.raw_body);
  } else {
    std::cout << "Segments deleted successfully" << std::endl;
  }

  UpdateSegmentRequest update_seg_req;
  update_seg_req.SetDatasetId(dataset_id)
      .SetDocumentId(document_id)
      .SetSegmentId("6235d7e2-4c85-4cca-992e-71b1e0c93d7e");
  SegmentInfo update_seg;
  update_seg.content = "123";
  update_seg.answer = "123";
  update_seg.keywords = {"123", "321", "1234567"};
  update_seg_req.SetSegment(update_seg);
  auto update_seg_result = client.UpdateSegment(update_seg_req);
  if (!update_seg_result.ok) {
    PrintError("Update segments", update_seg_result.raw_body);
  } else {
    std::cout << "Segments updated successfully" << std::endl;
  }

  std::cout << "\n=== Segment Demo completed ===" << std::endl;
  return 0;
}
