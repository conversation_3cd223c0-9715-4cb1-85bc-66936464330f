#include <iostream>

#include "ams_client.h"
#include "include/workflow.h"
using namespace amssdk;

int main() {
  AmsClient client("http://10.0.18.41/v1");

  WorkflowTaskStopRequest request;
  request.SetTaskId("task-123456");
  request.SetUser("abc-123");

  if (!client.SetAuthorizationKey("app-AH8NRZNiWATarWbMl4zDtvo3")) {
    return -1;
  }
  client.SetMaxTimeout(300000);

  auto result = client.WorkflowTaskStop(request);
  if (!result.ok) {
    std::cerr << "request failed: " << result.error.message << std::endl;
  } else {
    std::cout << "Workflow stop success: " << result.success << std::endl;
  }
  return 0;
}