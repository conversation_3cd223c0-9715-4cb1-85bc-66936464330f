#include <iostream>
#include "../libams/include/knowledge/knowledge.h"
#include "ams_client.h"

using namespace amssdk;

void PrintError(const std::string& operation, const std::string& raw_body) {
  try {
    auto json = nlohmann::json::parse(raw_body);
    if (json.contains("message")) {
      std::cerr << operation
                << " failed: " << json["message"].get<std::string>()
                << std::endl;
    } else {
      std::cerr << operation << " failed: " << raw_body << std::endl;
    }
  } catch (const std::exception&) {
    std::cerr << operation << " failed: " << raw_body << std::endl;
  }
}

int main() {
  // 创建客户端
  AmsClient client("http://10.0.18.41/v1");

  // 设置授权密钥
  if (!client.SetAuthorizationKey("dataset-CzGRgQ5UTRkaoZFMf1J427zR")) {
    std::cerr << "Failed to set authorization key" << std::endl;
    return -1;
  }

  client.SetMaxTimeout(300000);

  // 使用已存在的数据集ID
  std::string dataset_id = "1e41b175-5180-4181-8c70-03adbecf2229";
  std::cout << "=== Knowledge Retrieval Demo ===" << std::endl;

  // 1. 关键词搜索检索
  std::cout << "\n1. Retrieving with keyword search..." << std::endl;
  RetrieveDatasetRequest retrieve_req1;
  retrieve_req1.SetDatasetId(dataset_id).SetQuery("1");

  RetrievalModel retrieval_model1;
  retrieval_model1.search_method = SearchMethod::kKeywordSearch;
  retrieval_model1.top_k = 3;
  retrieve_req1.SetRetrievalModel(retrieval_model1);

  auto retrieve_result1 = client.RetrieveDataset(retrieve_req1);
  if (!retrieve_result1.ok) {
    PrintError("Retrieve dataset (keyword)", retrieve_result1.raw_body);
  } else {
    const auto& records1 = retrieve_result1.data.GetRecords();
    std::cout << "Keyword search found:" << records1 << std::endl;
  }

  // 2. 语义搜索检索
  std::cout << "\n2. Retrieving with semantic search..." << std::endl;
  RetrieveDatasetRequest retrieve_req2;
  retrieve_req2.SetDatasetId(dataset_id)
      .SetQuery("machine learning applications");

  RetrievalModel retrieval_model2;
  retrieval_model2.search_method = SearchMethod::kSemanticSearch;
  retrieval_model2.top_k = 5;
  retrieve_req2.SetRetrievalModel(retrieval_model2);

  auto retrieve_result2 = client.RetrieveDataset(retrieve_req2);
  if (!retrieve_result2.ok) {
    PrintError("Retrieve dataset (semantic)", retrieve_result2.raw_body);
  } else {
    const auto& records2 = retrieve_result2.data.GetRecords();
    std::cout << "Semantic search found " << records2 << std::endl;
  }

  // 3. 混合搜索检索
  std::cout << "\n3. Retrieving with hybrid search..." << std::endl;
  RetrieveDatasetRequest retrieve_req3;
  retrieve_req3.SetDatasetId(dataset_id).SetQuery("技术应用");

  RetrievalModel retrieval_model3;
  retrieval_model3.search_method = SearchMethod::kHybridSearch;
  retrieval_model3.top_k = 4;
  retrieve_req3.SetRetrievalModel(retrieval_model3);

  auto retrieve_result3 = client.RetrieveDataset(retrieve_req3);
  if (!retrieve_result3.ok) {
    PrintError("Retrieve dataset (hybrid)", retrieve_result3.raw_body);
  } else {
    const auto& records3 = retrieve_result3.data.GetRecords();
    std::cout << "Hybrid search found " << records3 << std::endl;
  }

  std::cout << "\n=== Retrieval Demo completed ===" << std::endl;
  return 0;
}
