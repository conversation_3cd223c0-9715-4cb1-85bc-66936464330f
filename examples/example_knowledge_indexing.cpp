#include <iostream>
#include "../libams/include/knowledge/knowledge.h"
#include "ams_client.h"

using namespace amssdk;

void PrintError(const std::string& operation, const std::string& raw_body) {
  try {
    auto json = nlohmann::json::parse(raw_body);
    if (json.contains("message")) {
      std::cerr << operation
                << " failed: " << json["message"].get<std::string>()
                << std::endl;
    } else {
      std::cerr << operation << " failed: " << raw_body << std::endl;
    }
  } catch (const std::exception&) {
    std::cerr << operation << " failed: " << raw_body << std::endl;
  }
}

int main() {
  // 创建客户端
  AmsClient client("http://10.0.18.41/v1");

  // 设置授权密钥
  if (!client.SetAuthorizationKey("dataset-CzGRgQ5UTRkaoZFMf1J427zR")) {
    std::cerr << "Failed to set authorization key" << std::endl;
    return -1;
  }

  client.SetMaxTimeout(300000);

  // eb173eae-f866-489e-b4b4-dcc9a0ade0fa/batch/20250911112920597183
  // 使用已存在的数据集ID和批次ID
  std::string dataset_id = "eb173eae-f866-489e-b4b4-dcc9a0ade0fa";
  std::string batch_id = "20250911112920597183";
  std::cout << "=== Knowledge Indexing Status Demo ===" << std::endl;

  // 检查索引状态
  std::cout << "\n1. Checking indexing status..." << std::endl;
  GetIndexingStatusRequest status_req;
  status_req.SetDatasetId(dataset_id).SetBatch(batch_id);

  auto status_result = client.GetIndexingStatus(status_req);
  if (!status_result.ok) {
    PrintError("Get indexing status", status_result.raw_body);
  } else {
    const auto& status_data = status_result.data.GetData();
    std::cout << "Indexing Data: " << status_data << std::endl;
  }

  std::cout << "\n=== Indexing Status Demo completed ===" << std::endl;
  return 0;
}
