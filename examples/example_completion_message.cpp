#include <iostream>

#include "ams_client.h"
#include "include/chat/chat_request.h"
#include "include/chat/chat_stream_event.h"

using namespace amssdk;

int main() {
  AmsClient client("http://10.0.18.41/v1");

  CompletionMessageRequest request;
  request.SetUser("abc-123");
  request.SetResponseMode(ResponseMode::STREAMING);
  request.SetInputs({{"query1", "成都最近7天的天气预报？"}});

  if (!client.SetAuthorizationKey("app-an85gncgP5oVvmttZH89ftVn")) {
    return -1;
  }
  client.SetMaxTimeout(300000);

  auto result = client.SendCompletionMessage(
      request, [](const std::unique_ptr<StreamEvent>& event) {
        auto event_type = event->type();
        if (event_type == StreamEvent::Type::MESSAGE) {
          const auto* message_event = event->as<MessageEvent>();
          std::cout << message_event->Answer();
        } else if (event_type == StreamEvent::Type::AGENT_THOUGHT) {
          const auto* message_event = event->as<AgentThoughtEvent>();
          std::cout << "Thought Message: " << message_event->Thought()
                    << std::endl;
        } else if (event_type == StreamEvent::Type::AGENT_MESSAGE) {
          const auto* message_event = event->as<MessageEvent>();
          std::cout << "Agent Message: " << message_event->Answer()
                    << std::endl;
        } else if (event_type == StreamEvent::Type::MESSAGE_END) {
          const auto* message_event = event->as<MessageEndEvent>();
          std::cout << "Message End"
                    << message_event->as<MessageEndEvent>()->ConversationId()
                    << std::endl;
        } else if (event_type == StreamEvent::Type::ERR) {
          const auto* message_event = event->as<ErrorEvent>();
          std::cout << "Error: " << message_event->Message() << std::endl;
        } else if (event_type == StreamEvent::Type::WORKFLOW_STARTED ||
                   event_type == StreamEvent::Type::NODE_FINISHED ||
                   event_type == StreamEvent::Type::WORKFLOW_FINISHED ||
                   event_type == StreamEvent::Type::NODE_STARTED) {
          const auto* message_event = event->as<WorkflowEvent>();
          std::cout << "Workflow Event: " << message_event->GetDataJson()
                    << std::endl;

        } else {
          std::cout << "Unknown event type" << std::endl;
        }
      });
  if (!result.ok) {
    std::cerr << "request failed: " << result.raw_body << std::endl;
  }

  return 0;
}
