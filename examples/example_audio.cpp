#include <iostream>
#include "ams_client.h"
#include "include/audio.h"

using namespace amssdk;
int main() {
  AmsClient client("http://10.0.18.41/v1");

  AudioToTextRequest request;
  request.SetFilePath(R"(D:\Workspace\AmsSdk\examples\test_data\test.wav)");
  request.SetUser("abc-123");

  if (!client.SetAuthorizationKey("app-HsG5fVJiIYdCLLYnIO2tzUf4")) {
    return -1;
  }
  client.SetMaxTimeout(300000);
  return -1;
}