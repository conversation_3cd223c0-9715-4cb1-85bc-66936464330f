#include "serializer.h"
#include <nlohmann/json.hpp>
#include <stdexcept>
#include "include/knowledge/knowledge.h"
#include "include/knowledge/knowledge_segments.h"
#include "serializer_utils.h"

namespace amssdk {

namespace {
// 统一的序列化错误处理
template <typename T>
std::string SerializeWithErrorHandling(const T& request,
                                       const std::string& request_type) {
  try {
    nlohmann::json json_request;
    ToJson(json_request, request);
    return json_request.dump();
  } catch (const nlohmann::json::exception& e) {
    throw std::runtime_error("JSON serialization failed for " + request_type +
                             ": " + e.what());
  } catch (const std::exception& e) {
    throw std::runtime_error("Serialization failed for " + request_type + ": " +
                             e.what());
  }
}
}  // anonymous namespace

std::string SerializeChatRequest(const ChatRequest& request) {
  return SerializeWithErrorHandling(request, "ChatRequest");
}

std::string SerializeTaskStopRequest(const TaskStopRequest& request) {
  return SerializeWithErrorHandling(request, "TaskStopRequest");
}

std::string SerializeFeedbackRequest(const FeedbackRequest& request) {
  return SerializeWithErrorHandling(request, "FeedbackRequest");
}

std::string SerializeDeleteConversationRequest(
    const DeleteConversationRequest& request) {
  return SerializeWithErrorHandling(request, "DeleteConversationRequest");
}

std::string SerializeRenameConversationRequest(
    const RenameConversationRequest& request) {
  return SerializeWithErrorHandling(request, "RenameConversationRequest");
}

std::string SerializeSuggestedRequest(const SuggestedRequest& request) {
  return SerializeWithErrorHandling(request, "SuggestedRequest");
}

std::string SerializeMessagesRequest(const MessagesRequest& request) {
  return SerializeWithErrorHandling(request, "MessagesRequest");
}

std::string SerializeConversationRequest(const ConversationRequest& request) {
  return SerializeWithErrorHandling(request, "ConversationRequest");
}
std::string SerializeChatCompletionRequest(
    const CompletionMessageRequest& request) {
  return SerializeWithErrorHandling(request, "ChatCompletionRequest");
}
std::string SerializeWorkflowRunRequest(const WorkflowRunRequest& request) {
  return SerializeWithErrorHandling(request, "WorkflowRunRequest");
}
std::string SerializeWorkflowTaskStopRequest(
    const WorkflowTaskStopRequest& request) {
  return SerializeWithErrorHandling(request, "WorkflowTaskStopRequest");
}
std::string SerializeWorkflowLogsRequest(const WorkflowLogsRequest& request) {
  return SerializeWithErrorHandling(request, "WorkflowLogsRequest");
}

std::string SerializeCreateDocumentByTextRequest(
    const CreateDocumentByTextRequest& request) {
  return SerializeWithErrorHandling(request, "CreateDocumentByTextRequest");
}

std::string SerializeCreateDatasetRequest(const CreateDatasetRequest& request) {
  return SerializeWithErrorHandling(request, "CreateDatasetRequest");
}

std::string SerializeCreateDocumentByFileRequest(
    const CreateDocumentByFileRequest& request) {
  return SerializeWithErrorHandling(request, "CreateDocumentByFileRequest");
}

std::string SerializeListDatasetsRequest(const ListDatasetsRequest& request) {
  std::string query_params = "page=" + std::to_string(request.GetPage()) +
                             "&limit=" + std::to_string(request.GetLimit());
  return query_params;
}

std::string SerializeDeleteDatasetRequest(const DeleteDatasetRequest& request) {
  // DELETE请求通常不需要body，dataset_id在URL中
  return "";
}

std::string SerializeUpdateDocumentByTextRequest(
    const UpdateDocumentByTextRequest& request) {
  return SerializeWithErrorHandling(request, "UpdateDocumentByTextRequest");
}

std::string SerializeUpdateDocumentByFileRequest(
    const UpdateDocumentByFileRequest& request) {
  return SerializeWithErrorHandling(request, "UpdateDocumentByFileRequest");
}

std::string SerializeGetIndexingStatusRequest(
    const GetIndexingStatusRequest& request) {
  return "";
}

std::string SerializeDeleteDocumentRequest(
    const DeleteDocumentRequest& request) {
  return "";
}

std::string SerializeListDocumentsRequest(const ListDocumentsRequest& request) {
  std::string query_params = "page=" + std::to_string(request.GetPage()) +
                             "&limit=" + std::to_string(request.GetLimit());
  if (!request.GetKeyword().empty()) {
    query_params += "&keyword=" + request.GetKeyword();
  }
  return query_params;
}

std::string SerializeCreateSegmentRequest(const CreateSegmentRequest& request) {
  return SerializeWithErrorHandling(request, "CreateSegmentRequest");
}

std::string SerializeListSegmentsRequest(const ListSegmentsRequest& request) {
  std::string query_params = "";
  if (!request.GetKeyword().empty()) {
    query_params += "keyword=" + request.GetKeyword();
  }
  if (!request.GetStatus().empty()) {
    if (!query_params.empty())
      query_params += "&";
    query_params += "status=" + request.GetStatus();
  }
  return query_params;
}

std::string SerializeDeleteSegmentRequest(const DeleteSegmentRequest& request) {
  return "";
}

std::string SerializeUpdateSegmentRequest(const UpdateSegmentRequest& request) {
  return SerializeWithErrorHandling(request, "UpdateSegmentRequest");
}

std::string SerializeRetrieveDatasetRequest(
    const RetrieveDatasetRequest& request) {
  return SerializeWithErrorHandling(request, "RetrieveDatasetRequest");
}

}  // namespace amssdk
