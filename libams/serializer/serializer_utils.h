#ifndef AMSSDK_SERIALIZER_UTILS_H
#define AMSSDK_SERIALIZER_UTILS_H
#include <nlohmann/json.hpp>
#include <string>

#include "include/common/common.h"

namespace amssdk {

// Forward declarations
class TaskStopRequest;
class FeedbackRequest;
class SuggestedRequest;
class MessagesRequest;
class DeleteConversationRequest;
class RenameConversationRequest;
class WorkflowRunRequest;
class ConversationRequest;
class ChatRequest;
class CompletionMessageRequest;
class WorkflowRunInfoResponse;
class WorkflowLogsResponse;
class WorkflowRunResponse;
class WorkflowRunInfoRequest;
class WorkflowLogsRequest;
class WorkflowTaskStopRequest;
class ChatCompletionRequest;

// Knowledge base classes
class CreateDocumentByTextRequest;
class CreateDatasetRequest;
class CreateDocumentByFileRequest;
class UpdateDocumentByTextRequest;
class UpdateDocumentByFileRequest;
class CreateSegmentRequest;
class UpdateSegmentRequest;
class RetrieveDatasetRequest;
class CreateDocumentResponse;
class CreateDatasetResponse;
class ListDatasetsResponse;
class GetIndexingStatusResponse;
class DeleteDocumentResponse;
class ListDocumentsResponse;
class CreateSegmentResponse;
class ListSegmentsResponse;
class DeleteSegmentResponse;
class UpdateSegmentResponse;
class RetrieveDatasetResponse;

// Response types
class FileResponse;
class ConversationResponse;
class SuggestedResponse;
class MessagesResponse;
class SimpleResponse;
class RenameConversationResponse;

class SerializerUtils {
 public:
  SerializerUtils() = delete;
  ~SerializerUtils() = delete;
  SerializerUtils(const SerializerUtils&) = delete;
  SerializerUtils& operator=(const SerializerUtils&) = delete;
  SerializerUtils(SerializerUtils&&) = delete;

  // Enum <-> string mappings
  static std::string ResponseModeToString(const ResponseMode& mode);
  static std::string FileTypeToString(const FileAttachment::FileType& type);
  static std::string TransferMethodToString(
      const FileAttachment::TransferMethod& method);
  static ResponseMode ResponseModeFromString(const std::string& mode);
  static FileAttachment::FileType FileTypeFromString(const std::string& type);
  static FileAttachment::TransferMethod TransferMethodFromString(
      const std::string& method);
  static std::string SortRuleToString(const SortRule& rule);
  static SortRule SortRuleFromString(const std::string& rule);

  static std::string ModeToString(ProcessRuleMode mode);
  static ProcessRuleMode ModeFromString(const std::string& mode);
  static std::string IndexingTechniqueToString(IndexingTechnique technique);
  static IndexingTechnique IndexingTechniqueFromString(
      const std::string& technique);
  static std::string CreateDatasetPermissionToString(
      CreateDatasetPermission permission);
  static CreateDatasetPermission CreateDatasetPermissionFromString(
      const std::string& permission);
  static std::string CreateDatasetProviderToString(
      CreateDatasetProvider provider);
  static CreateDatasetProvider CreateDatasetProviderFromString(
      const std::string& provider);
  static std::string SearchMethodToString(SearchMethod method);
  static SearchMethod SearchMethodFromString(const std::string& method);
};

// to_json declarations for request models (serialization only)
void ToJson(nlohmann::json& j, const ChatRequest& r);
void ToJson(nlohmann::json& j, const SuggestedRequest& r);
void ToJson(nlohmann::json& j, const MessagesRequest& r);
void ToJson(nlohmann::json& j, const ConversationRequest& r);
void ToJson(nlohmann::json& j, const DeleteConversationRequest& r);
void ToJson(nlohmann::json& j, const RenameConversationRequest& r);
void ToJson(nlohmann::json& j, const TaskStopRequest& r);
void ToJson(nlohmann::json& j, const FeedbackRequest& r);
void ToJson(nlohmann::json& j, const CompletionMessageRequest& r);

void ToJson(nlohmann::json& j, const WorkflowRunRequest& r);
void ToJson(nlohmann::json& j, const WorkflowTaskStopRequest& r);
void ToJson(nlohmann::json& j, const WorkflowLogsRequest& r);
void ToJson(nlohmann::json& j, const WorkflowRunInfoRequest& r);

// Knowledge base request serialization
void ToJson(nlohmann::json& j, const CreateDocumentByTextRequest& r);
void ToJson(nlohmann::json& j, const CreateDatasetRequest& r);
void ToJson(nlohmann::json& j, const CreateDocumentByFileRequest& r);
void ToJson(nlohmann::json& j, const UpdateDocumentByTextRequest& r);
void ToJson(nlohmann::json& j, const UpdateDocumentByFileRequest& r);
void ToJson(nlohmann::json& j, const CreateSegmentRequest& r);
void ToJson(nlohmann::json& j, const UpdateSegmentRequest& r);
void ToJson(nlohmann::json& j, const RetrieveDatasetRequest& r);

// from_json declarations for response models (deserialization only)
void FromJson(const nlohmann::json& j, FileResponse& r);
void FromJson(const nlohmann::json& j, ConversationResponse& r);
void FromJson(const nlohmann::json& j, SuggestedResponse& r);
void FromJson(const nlohmann::json& j, MessagesResponse& r);
void FromJson(const nlohmann::json& j, SimpleResponse& r);
void FromJson(const nlohmann::json& j, RenameConversationResponse& r);

void FromJson(const nlohmann::json& j, WorkflowRunResponse& r);
void FromJson(const nlohmann::json& j, WorkflowLogsResponse& r);
void FromJson(const nlohmann::json& j, WorkflowRunInfoResponse& r);

// Knowledge base response deserialization
void FromJson(const nlohmann::json& j, CreateDocumentResponse& r);
void FromJson(const nlohmann::json& j, CreateDatasetResponse& r);
void FromJson(const nlohmann::json& j, ListDatasetsResponse& r);
void FromJson(const nlohmann::json& j, GetIndexingStatusResponse& r);
void FromJson(const nlohmann::json& j, DeleteDocumentResponse& r);
void FromJson(const nlohmann::json& j, ListDocumentsResponse& r);
void FromJson(const nlohmann::json& j, CreateSegmentResponse& r);
void FromJson(const nlohmann::json& j, ListSegmentsResponse& r);
void FromJson(const nlohmann::json& j, DeleteSegmentResponse& r);
void FromJson(const nlohmann::json& j, UpdateSegmentResponse& r);
void FromJson(const nlohmann::json& j, RetrieveDatasetResponse& r);

}  // namespace amssdk

#endif  //AMSSDK_SERIALIZER_UTILS_H