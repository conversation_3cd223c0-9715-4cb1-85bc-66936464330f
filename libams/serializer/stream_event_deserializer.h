#ifndef AMSSDK_STREAM_EVENT_DESERIALIZER_H
#define AMSSDK_STREAM_EVENT_DESERIALIZER_H

#include <nlohmann/json.hpp>
#include <string>
#include <vector>
#include "include/chat/chat_stream_event.h"

namespace amssdk {

class StreamEventDeserializer {
 public:
  StreamEventDeserializer() = default;
  ~StreamEventDeserializer() = default;

  std::unique_ptr<StreamEvent> DeserializeStreamEvent(
      const std::string& json_str);

  std::vector<std::unique_ptr<StreamEvent>> DeserializeMultipleEvents(
      const std::string& chunk);
  const std::string& GetLastError() const { return last_error_; }

  struct Stats {
    uint64_t total_events_parsed = 0;
    uint64_t successful_parses = 0;
    uint64_t failed_parses = 0;
    uint64_t sse_events_parsed = 0;
    uint64_t direct_json_parsed = 0;
  };

  const Stats& GetStats() const { return stats_; }

 private:
  bool ExtractJsonFromSSE(const std::string& sse_line, std::string& json);
  std::unique_ptr<StreamEvent> ParseMessageEvent(const nlohmann::json& j);
  std::unique_ptr<StreamEvent> ParseAgentThoughtEvent(const nlohmann::json& j);
  std::unique_ptr<StreamEvent> ParseMessageEndEvent(const nlohmann::json& j);
  std::unique_ptr<StreamEvent> ParseMessageFileEvent(const nlohmann::json& j);
  std::unique_ptr<StreamEvent> ParseTtsMessageEvent(const nlohmann::json& j);
  std::unique_ptr<StreamEvent> ParseTtsMessageEndEvent(const nlohmann::json& j);
  std::unique_ptr<StreamEvent> ParseMessageReplaceEvent(
      const nlohmann::json& j);
  std::unique_ptr<StreamEvent> ParseErrorEvent(const nlohmann::json& j);
  std::unique_ptr<StreamEvent> ParseWorkflowEvent(const nlohmann::json& j,
                                                  StreamEvent::Type type);

  bool ValidateMessageEvent(const nlohmann::json& j);
  bool ValidateAgentThoughtEvent(const nlohmann::json& j);
  bool ValidateWorkflowEvent(const nlohmann::json& j);

  void SetError(const std::string& error);

  mutable std::string last_error_;
  mutable Stats stats_;
};

template <typename T>
bool SafeGetJsonValue(const nlohmann::json& j, const std::string& key, T& out,
                      const T& default_value = T{}) {
  try {
    if (j.contains(key) && !j[key].is_null()) {
      out = j[key].get<T>();
      return true;
    }
  } catch (const nlohmann::json::exception& e) {
    // Type mismatch or other JSON error
  }
  out = default_value;
  return false;
}

}  // namespace amssdk

#endif  // AMSSDK_STREAM_EVENT_DESERIALIZER_H