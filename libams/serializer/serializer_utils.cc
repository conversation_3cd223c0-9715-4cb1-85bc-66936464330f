#include "serializer_utils.h"
#include <nlohmann/json.hpp>

#include "include/chat/chat_request.h"
#include "include/common/common.h"
#include "include/conversation/conversation_request.h"
#include "include/conversation/conversation_response.h"
#include "include/file.h"
#include "include/knowledge/knowledge.h"
#include "include/knowledge/knowledge_segments.h"
#include "include/task.h"
#include "include/workflow.h"

namespace amssdk {

std::string SerializerUtils::ResponseModeToString(const ResponseMode& mode) {
  switch (mode) {
    case ResponseMode::STREAMING:
      return "streaming";
    case ResponseMode::BLOCKING:
      return "blocking";
    default:
      return "streaming";
  }
}
std::string SerializerUtils::FileTypeToString(
    const FileAttachment::FileType& type) {
  switch (type) {
    case FileAttachment::FileType::IMAGE:
      return "image";
    case FileAttachment::FileType::VIDEO:
      return "video";
    case FileAttachment::FileType::AUDIO:
      return "audio";
    case FileAttachment::FileType::DOCUMENT:
      return "document";
    default:
      return "image";
  }
}

std::string SerializerUtils::TransferMethodToString(
    const FileAttachment::TransferMethod& method) {
  switch (method) {
    case FileAttachment::TransferMethod::LOCAL_FILE:
      return "local_file";
    case FileAttachment::TransferMethod::URL:
      return "url";
    default:
      return "local_file";
  }
}
ResponseMode SerializerUtils::ResponseModeFromString(const std::string& mode) {
  if (mode == "streaming") {
    return ResponseMode::STREAMING;
  }
  return ResponseMode::BLOCKING;
}
FileAttachment::FileType SerializerUtils::FileTypeFromString(
    const std::string& type) {
  if (type == "image") {
    return FileAttachment::FileType::IMAGE;
  }
  if (type == "video") {
    return FileAttachment::FileType::VIDEO;
  }
  if (type == "audio") {
    return FileAttachment::FileType::AUDIO;
  }
  if (type == "document") {
    return FileAttachment::FileType::DOCUMENT;
  }
  return FileAttachment::FileType::IMAGE;
}
FileAttachment::TransferMethod SerializerUtils::TransferMethodFromString(
    const std::string& method) {
  if (method == "local_file") {
    return FileAttachment::TransferMethod::LOCAL_FILE;
  }
  return FileAttachment::TransferMethod::URL;
}

std::string SerializerUtils::SortRuleToString(const SortRule& rule) {
  if (rule == SortRule::kCreatedAt) {
    return "created_at";
  }
  if (rule == SortRule::kUpdatedAt) {
    return "updated_at";
  }
  if (rule == SortRule::kDescCreatedAt) {
    return "-created_at";
  }
  if (rule == SortRule::kDescUpdatedAt) {
    return "-updated_at";
  }
  return "updated_at";
}

SortRule SerializerUtils::SortRuleFromString(const std::string& rule) {
  if (rule == "created_at")
    return SortRule::kCreatedAt;
  if (rule == "updated_at")
    return SortRule::kUpdatedAt;
  if (rule == "-created_at")
    return SortRule::kDescCreatedAt;
  if (rule == "-updated_at")
    return SortRule::kDescUpdatedAt;
  return SortRule::kUpdatedAt;
}
std::string SerializerUtils::ModeToString(ProcessRuleMode mode) {
  if (mode == ProcessRuleMode::AUTOMATIC) {
    return "automatic";
  }
  return "custom";
}
ProcessRuleMode SerializerUtils::ModeFromString(const std::string& mode) {
  if (mode == "automatic") {
    return ProcessRuleMode::AUTOMATIC;
  }
  return ProcessRuleMode::CUSTOM;
}
std::string SerializerUtils::IndexingTechniqueToString(
    IndexingTechnique technique) {
  if (technique == IndexingTechnique::kHighQuality) {
    return "high_quality";
  }
  return "economy";
}
IndexingTechnique SerializerUtils::IndexingTechniqueFromString(
    const std::string& technique) {
  if (technique == "high_quality") {
    return IndexingTechnique::kHighQuality;
  }
  return IndexingTechnique::kEconomy;
}
std::string SerializerUtils::CreateDatasetPermissionToString(
    CreateDatasetPermission permission) {
  if (permission == CreateDatasetPermission::kOnlyMe) {
    return "only_me";
  }
  if (permission == CreateDatasetPermission::kAllTeamMembers) {
    return "all_team_members";
  }
  return "partial_members";
}
CreateDatasetPermission SerializerUtils::CreateDatasetPermissionFromString(
    const std::string& permission) {
  if (permission == "only_me") {
    return CreateDatasetPermission::kOnlyMe;
  }
  if (permission == "all_team_members") {
    return CreateDatasetPermission::kAllTeamMembers;
  }
  return CreateDatasetPermission::kPartialMembers;
}
std::string SerializerUtils::CreateDatasetProviderToString(
    CreateDatasetProvider provider) {
  if (provider == CreateDatasetProvider::kVendor) {
    return "vendor";
  }
  return "external";
}
CreateDatasetProvider SerializerUtils::CreateDatasetProviderFromString(
    const std::string& provider) {
  if (provider == "vendor") {
    return CreateDatasetProvider::kVendor;
  }
  return CreateDatasetProvider::kExternal;
}
std::string SerializerUtils::SearchMethodToString(SearchMethod method) {
  if (method == SearchMethod::kKeywordSearch) {
    return "keyword_search";
  }
  if (method == SearchMethod::kSemanticSearch) {
    return "semantic_search";
  }
  if (method == SearchMethod::kFullTextSearch) {
    return "full_text_search";
  }
  if (method == SearchMethod::kHybridSearch) {
    return "hybrid_search";
  }
  return "keyword_search";
}
SearchMethod SerializerUtils::SearchMethodFromString(
    const std::string& method) {
  if (method == "keyword_search") {
    return SearchMethod::kKeywordSearch;
  }
  if (method == "semantic_search") {
    return SearchMethod::kSemanticSearch;
  }
  if (method == "full_text_search") {
    return SearchMethod::kFullTextSearch;
  }
  if (method == "hybrid_search") {
    return SearchMethod::kHybridSearch;
  }
  return SearchMethod::kKeywordSearch;
}

// to_json implementations for request models (serialization only)
void ToJson(nlohmann::json& j, const ChatRequest& r) {
  j = nlohmann::json::object();
  // Maintain existing behavior: always include keys even if empty
  j["inputs"] = r.GetInputs();
  j["query"] = r.GetQuery();
  j["response_mode"] =
      SerializerUtils::ResponseModeToString(r.GetResponseMode());
  j["conversation_id"] = r.GetConversationId();
  j["user"] = r.GetUser();

  // files
  nlohmann::json files = nlohmann::json::array();
  for (const auto& f : r.GetFiles()) {
    nlohmann::json jf;
    jf["type"] = SerializerUtils::FileTypeToString(f.type);
    jf["transfer_method"] =
        SerializerUtils::TransferMethodToString(f.transfer_method);
    if (f.transfer_method == FileAttachment::TransferMethod::LOCAL_FILE) {
      jf["upload_file_id"] = f.upload_file_id;
    } else {
      jf["url"] = f.url;
    }
    files.push_back(std::move(jf));
  }
  j["files"] = std::move(files);
}

void ToJson(nlohmann::json& j, const SuggestedRequest& r) {
  j = nlohmann::json::object();
  j["message_id"] = r.GetMessageId();
  j["user"] = r.GetUser();
}

void ToJson(nlohmann::json& j, const MessagesRequest& r) {
  j = nlohmann::json::object();
  j["conversation_id"] = r.GetConversationId();
  j["user"] = r.GetUser();
  j["first_id"] = r.GetFirstId();
  j["limit"] = r.GetLimit();
}

void ToJson(nlohmann::json& j, const ConversationRequest& r) {
  j = nlohmann::json::object();
  j["user"] = r.GetUser();
  j["last_id"] = r.GetLastId();
  j["limit"] = r.GetLimit();
  j["sort_by"] = SerializerUtils::SortRuleToString(r.GetSortRule());
}

void ToJson(nlohmann::json& j, const DeleteConversationRequest& r) {
  j = nlohmann::json::object();
  j["user"] = r.GetUser();
  j["conversation_id"] = r.GetConversationId();
}

void ToJson(nlohmann::json& j, const RenameConversationRequest& r) {
  j = nlohmann::json::object();
  j["name"] = r.GetName();
  j["user"] = r.GetUser();
  j["auto_generate_name"] = r.GetAutoGenerateName();
  j["conversation_id"] = r.GetConversationId();
}

void ToJson(nlohmann::json& j, const TaskStopRequest& r) {
  j = nlohmann::json::object();
  j["task_id"] = r.GetTaskId();
  j["user"] = r.GetUser();
}

void ToJson(nlohmann::json& j, const FeedbackRequest& r) {
  j = nlohmann::json::object();
  j["message_id"] = r.GetMessageId();
  j["user"] = r.GetUser();
  j["content"] = r.GetContent();

  // Convert Rating enum to string
  std::string rating_str;
  switch (r.GetRating()) {
    case FeedbackRequest::Rating::kLike:
      rating_str = "like";
      break;
    case FeedbackRequest::Rating::kDislike:
      rating_str = "dislike";
      break;
    case FeedbackRequest::Rating::kNull:
    default:
      rating_str = "null";
      break;
  }
  j["rating"] = rating_str;
}

void ToJson(nlohmann::json& j, const CompletionMessageRequest& r) {
  j = nlohmann::json::object();
  j["inputs"] = r.GetInputs();
  j["response_mode"] =
      SerializerUtils::ResponseModeToString(r.GetResponseMode());
  j["user"] = r.GetUser();
  // files
  nlohmann::json files = nlohmann::json::array();
  for (const auto& f : r.GetFiles()) {
    nlohmann::json jf;
    jf["type"] = SerializerUtils::FileTypeToString(f.type);
    jf["transfer_method"] =
        SerializerUtils::TransferMethodToString(f.transfer_method);
    if (f.transfer_method == FileAttachment::TransferMethod::LOCAL_FILE) {
      jf["upload_file_id"] = f.upload_file_id;
    } else {
      jf["url"] = f.url;
    }
    files.push_back(std::move(jf));
  }
  j["files"] = std::move(files);
}

void ToJson(nlohmann::json& j, const WorkflowRunRequest& r) {
  j = nlohmann::json::object();
  j["user"] = r.GetUser();

  for (const auto& json : r.GetInputs()) {
    j["inputs"][json.first] = json.second;
  }
  j["response_mode"] =
      SerializerUtils::ResponseModeToString(r.GetResponseMode());
  j["files"] = nlohmann::json::array();
  for (const auto& file : r.GetFiles()) {
    nlohmann::json jf;
    jf["type"] = SerializerUtils::FileTypeToString(file.type);
    jf["transfer_method"] =
        SerializerUtils::TransferMethodToString(file.transfer_method);
    if (file.transfer_method == FileAttachment::TransferMethod::LOCAL_FILE) {
      jf["upload_file_id"] = file.upload_file_id;
    } else {
      jf["url"] = file.url;
    }
    j["files"].push_back(std::move(jf));
  }
}
void ToJson(nlohmann::json& j, const WorkflowTaskStopRequest& r) {
  j = nlohmann::json::object();
  j["user"] = r.GetUser();
}

void ToJson(nlohmann::json& j, const WorkflowLogsRequest& r) {
  j = nlohmann::json::object();
  j["key_word"] = r.GetKeyWord();
  j["status"] = r.GetStatus();
  j["page"] = r.GetPage();
  j["limit"] = r.GetLimit();
}

// Helper function for ProcessRule serialization
static nlohmann::json ProcessRuleToJson(const ProcessRule& process_rule) {
  nlohmann::json process_rule_json;
  process_rule_json["mode"] = SerializerUtils::ModeToString(process_rule.mode);

  nlohmann::json rule = nlohmann::json::object();
  if (process_rule.mode == ProcessRuleMode::CUSTOM) {
    nlohmann::json pre_processing_rules = nlohmann::json::array();
    for (const auto& pre_rule : process_rule.pre_processing_rules) {
      nlohmann::json rule_json;
      rule_json["id"] = pre_rule.id;
      rule_json["enabled"] = pre_rule.enabled;
      pre_processing_rules.push_back(rule_json);
    }
    rule["pre_processing_rules"] = pre_processing_rules;
    nlohmann::json segmentation_json;
    segmentation_json["separator"] = process_rule.segmentation.separator;
    segmentation_json["max_tokens"] = process_rule.segmentation.max_tokens;
    rule["segmentation"] = segmentation_json;

    process_rule_json["rules"] = rule;
  }
  return process_rule_json;
}

// Knowledge base request serialization implementations
void ToJson(nlohmann::json& j, const CreateDocumentByTextRequest& r) {
  j = nlohmann::json::object();
  j["name"] = r.GetName();
  j["text"] = r.GetText();
  j["indexing_technique"] =
      SerializerUtils::IndexingTechniqueToString(r.GetIndexingTechnique());
  j["process_rule"] = ProcessRuleToJson(r.GetProcessRule());
}

void ToJson(nlohmann::json& j, const CreateDatasetRequest& r) {
  j = nlohmann::json::object();
  j["name"] = r.GetName();
  j["description"] = r.GetDescription();
  j["indexing_technique"] =
      SerializerUtils::IndexingTechniqueToString(r.GetIndexingTechnique());
  j["permission"] =
      SerializerUtils::CreateDatasetPermissionToString(r.GetPermission());
  j["provider"] =
      SerializerUtils::CreateDatasetProviderToString(r.GetProvider());

  if (!r.GetExternalKnowledgeApiId().empty()) {
    j["external_knowledge_api_id"] = r.GetExternalKnowledgeApiId();
  }
  if (!r.GetExternalKnowledgeId().empty()) {
    j["external_knowledge_id"] = r.GetExternalKnowledgeId();
  }
}

void ToJson(nlohmann::json& j, const CreateDocumentByFileRequest& r) {
  j = nlohmann::json::object();
  if (!r.GetOriginalDocumentId().empty()) {
    j["original_document_id"] = r.GetOriginalDocumentId();
  }
  j["indexing_technique"] =
      SerializerUtils::IndexingTechniqueToString(r.GetIndexingTechnique());
  j["process_rule"] = ProcessRuleToJson(r.GetProcessRule());
}

void ToJson(nlohmann::json& j, const UpdateDocumentByTextRequest& r) {
  j = nlohmann::json::object();
  if (!r.GetName().empty()) {
    j["name"] = r.GetName();
  }
  if (!r.GetText().empty()) {
    j["text"] = r.GetText();
  }
  j["process_rule"] = ProcessRuleToJson(r.GetProcessRule());
}

void ToJson(nlohmann::json& j, const UpdateDocumentByFileRequest& r) {
  j = nlohmann::json::object();
  if (!r.GetName().empty()) {
    j["name"] = r.GetName();
  }
  j["indexing_technique"] =
      SerializerUtils::IndexingTechniqueToString(r.GetIndexingTechnique());
  j["process_rule"] = ProcessRuleToJson(r.GetProcessRule());
}

void ToJson(nlohmann::json& j, const CreateSegmentRequest& r) {
  j = nlohmann::json::object();
  nlohmann::json segments_array = nlohmann::json::array();

  for (const auto& segment : r.GetSegments()) {
    nlohmann::json segment_json;
    segment_json["content"] = segment.content;
    if (!segment.answer.empty()) {
      segment_json["answer"] = segment.answer;
    }
    if (!segment.keywords.empty()) {
      segment_json["keywords"] = segment.keywords;
    }
    segments_array.push_back(segment_json);
  }

  j["segments"] = segments_array;
}

void ToJson(nlohmann::json& j, const UpdateSegmentRequest& r) {
  j = nlohmann::json::object();
  nlohmann::json segment_json;

  const auto& segment = r.GetSegment();
  segment_json["content"] = segment.content;
  if (!segment.answer.empty()) {
    segment_json["answer"] = segment.answer;
  }
  if (!segment.keywords.empty()) {
    segment_json["keywords"] = segment.keywords;
  }

  j["segment"] = segment_json;
}

void ToJson(nlohmann::json& j, const RetrieveDatasetRequest& r) {
  j = nlohmann::json::object();
  j["query"] = r.GetQuery();

  const auto& retrieval_model = r.GetRetrievalModel();
  nlohmann::json retrieval_model_json;
  retrieval_model_json["search_method"] =
      SerializerUtils::SearchMethodToString(retrieval_model.search_method);
  retrieval_model_json["reranking_enable"] = retrieval_model.reranking_enable;

  nlohmann::json reranking_mode_json;
  reranking_mode_json["reranking_model_name"] =
      retrieval_model.reranking_mode.reranking_model_name;
  reranking_mode_json["reranking_provider_name"] =
      retrieval_model.reranking_mode.reranking_provider_name;
  retrieval_model_json["reranking_mode"] = reranking_mode_json;
  retrieval_model_json["weights"] = retrieval_model.weights;
  retrieval_model_json["top_k"] = retrieval_model.top_k;
  retrieval_model_json["score_threshold_enabled"] =
      retrieval_model.score_threshold_enabled;
  retrieval_model_json["score_threshold"] = retrieval_model.score_threshold;

  j["retrieval_model"] = retrieval_model_json;
}

// from_json implementations for response models (deserialization only)
void FromJson(const nlohmann::json& j, FileResponse& r) {
  r.file_type_ = SerializerUtils::FileTypeFromString(j.value("file_type", ""));
  r.id_ = j.value("id", "");
  r.name_ = j.value("name", "");
  r.size_ = j.value("size", 0);
  r.extension_ = j.value("extension", "");
  r.mime_type_ = j.value("mime_type", "");
  r.created_by_ = j.value("created_by", "");
  r.created_at_ = j.value("created_at", 0);
}

void FromJson(const nlohmann::json& j, ConversationResponse& r) {
  r.limit = j.value("limit", 0);
  r.has_more = j.value("has_more", false);
  if (j.contains("data")) {
    r.data = j["data"].dump();
  }
}

void FromJson(const nlohmann::json& j, SuggestedResponse& r) {
  r.result = SimpleResponse::ResultType::kSuccess;
  r.data = j.value("data", "");
}

void FromJson(const nlohmann::json& j, MessagesResponse& r) {
  r.limit = j.value("limit", 0);
  r.has_more = j.value("has_more", false);
  if (j.contains("data")) {
    r.data = j["data"].dump();
  }
}

void FromJson(const nlohmann::json& j, SimpleResponse& r) {
  r.result = SimpleResponse::ResultType::kSuccess;
}

void FromJson(const nlohmann::json& j, RenameConversationResponse& r) {
  r.id = j.value("id", "");
  r.name = j.value("name", "");
  if (j.contains("inputs")) {
    r.inputs = j["inputs"].dump();
  }
  r.status = j.value("status", "");
  if (j.contains("introduction")) {
    r.introduction =
        j["introduction"].is_null() ? "" : j["introduction"].get<std::string>();
  }
  r.created_at = j.value("created_at", 0);
  r.updated_at = j.value("updated_at", 0);
}
void FromJson(const nlohmann::json& j, WorkflowRunResponse& r) {
  r.SetTaskId(j.value("task_id", ""));
  r.SetWorkflowRunId(j.value("workflow_run_id", ""));
  if (j.contains("data")) {
    r.SetDataJson(j["data"].dump());
  }
}
void FromJson(const nlohmann::json& j, WorkflowLogsResponse& r) {
  r.SetPage(j.value("page", 0));
  r.SetLimit(j.value("limit", 0));
  r.SetTotal(j.value("total", 0));
  r.SetHasMore(j.value("has_more", false));
  if (j.contains("data")) {
    r.SetData(j["data"].dump());
  }
}

void FromJson(const nlohmann::json& j, WorkflowRunInfoResponse& r) {
  r.SetId(j.value("id", ""));
  r.SetWorkflowId(j.value("workflow_id", ""));
  r.SetStatus(j.value("status", ""));
  if (j.contains("inputs")) {
    r.SetInputs(j["inputs"]);
  }
  if (j.contains("outputs")) {
    r.SetOutputs(j["outputs"]);
  }
  if (j.contains("error")) {
    auto error = j.at("error");
    r.SetError(error.is_null() ? "" : error.get<std::string>());
  }
  r.SetTotalSteps(j.value("total_steps", 0));
  r.SetTotalTokens(j.value("total_tokens", 0));
}

// Knowledge base response deserialization implementations
void FromJson(const nlohmann::json& j, CreateDocumentResponse& r) {
  if (j.contains("document")) {
    r.SetDocument(j["document"].dump());
  }
  if (j.contains("batch")) {
    r.SetBatch(j["batch"]);
  }
}

void FromJson(const nlohmann::json& j, CreateDatasetResponse& r) {
  DatasetInfo dataset;

  if (j.contains("id"))
    dataset.id = j["id"];
  if (j.contains("name"))
    dataset.name = j["name"];
  if (j.contains("description") && !j["description"].is_null())
    dataset.description = j["description"];
  if (j.contains("provider"))
    dataset.provider = j["provider"];
  if (j.contains("permission"))
    dataset.permission = j["permission"];
  if (j.contains("data_source_type") && !j["data_source_type"].is_null())
    dataset.data_source_type = j["data_source_type"];
  if (j.contains("indexing_technique") && !j["indexing_technique"].is_null())
    dataset.indexing_technique = j["indexing_technique"];
  if (j.contains("app_count"))
    dataset.app_count = j["app_count"];
  if (j.contains("document_count"))
    dataset.document_count = j["document_count"];
  if (j.contains("word_count"))
    dataset.word_count = j["word_count"];
  if (j.contains("created_by"))
    dataset.created_by = j["created_by"];
  if (j.contains("created_at"))
    dataset.created_at = j["created_at"];
  if (j.contains("updated_by"))
    dataset.updated_by = j["updated_by"];
  if (j.contains("updated_at"))
    dataset.updated_at = j["updated_at"];
  if (j.contains("embedding_model") && !j["embedding_model"].is_null())
    dataset.embedding_model = j["embedding_model"];
  if (j.contains("embedding_model_provider") &&
      !j["embedding_model_provider"].is_null())
    dataset.embedding_model_provider = j["embedding_model_provider"];
  if (j.contains("embedding_available") && !j["embedding_available"].is_null())
    dataset.embedding_available = j["embedding_available"];

  r.SetDataset(dataset);
}

void FromJson(const nlohmann::json& j, ListDatasetsResponse& r) {
  if (j.contains("data")) {
    r.SetData(j["data"].dump());
  }
  if (j.contains("has_more"))
    r.SetHasMore(j["has_more"]);
  if (j.contains("limit"))
    r.SetLimit(j["limit"]);
  if (j.contains("total"))
    r.SetTotal(j["total"]);
  if (j.contains("page"))
    r.SetPage(j["page"]);
}

void FromJson(const nlohmann::json& j, GetIndexingStatusResponse& r) {
  if (j.contains("data")) {
    r.SetData(j["data"].dump());
  }
}

void FromJson(const nlohmann::json& j, DeleteDocumentResponse& r) {
  if (j.contains("result")) {
    r.SetResult(j["result"]);
  }
}

void FromJson(const nlohmann::json& j, ListDocumentsResponse& r) {
  if (j.contains("data")) {
    std::vector<DocumentInfo> documents;
    for (const auto& item : j["data"]) {
      DocumentInfo document;
      if (item.contains("id"))
        document.id = item["id"];
      if (item.contains("position"))
        document.position = item["position"];
      if (item.contains("data_source_type"))
        document.data_source_type = item["data_source_type"];
      if (item.contains("name"))
        document.name = item["name"];
      if (item.contains("created_from"))
        document.created_from = item["created_from"];
      if (item.contains("created_by"))
        document.created_by = item["created_by"];
      if (item.contains("created_at"))
        document.created_at = item["created_at"];
      if (item.contains("tokens"))
        document.tokens = item["tokens"];
      if (item.contains("indexing_status"))
        document.indexing_status = item["indexing_status"];
      if (item.contains("error") && !item["error"].is_null())
        document.error = item["error"];
      if (item.contains("enabled"))
        document.enabled = item["enabled"];
      if (item.contains("disabled_at") && !item["disabled_at"].is_null())
        document.disabled_at = item["disabled_at"];
      if (item.contains("disabled_by") && !item["disabled_by"].is_null())
        document.disabled_by = item["disabled_by"];
      if (item.contains("archived"))
        document.archived = item["archived"];
      if (item.contains("display_status"))
        document.display_status = item["display_status"];
      if (item.contains("word_count"))
        document.word_count = item["word_count"];
      if (item.contains("hit_count"))
        document.hit_count = item["hit_count"];
      if (item.contains("doc_form"))
        document.doc_form = item["doc_form"];
      documents.push_back(document);
    }
    r.SetData(documents);
  }

  if (j.contains("has_more"))
    r.SetHasMore(j["has_more"]);
  if (j.contains("limit"))
    r.SetLimit(j["limit"]);
  if (j.contains("total"))
    r.SetTotal(j["total"]);
  if (j.contains("page"))
    r.SetPage(j["page"]);
}

void FromJson(const nlohmann::json& j, CreateSegmentResponse& r) {
  if (j.contains("data")) {
    r.SetData(j["data"].dump());
  }
}

void FromJson(const nlohmann::json& j, ListSegmentsResponse& r) {
  if (j.contains("data")) {
    r.SetData(j["data"].dump());
  }
}

void FromJson(const nlohmann::json& j, DeleteSegmentResponse& r) {
  if (j.contains("result")) {
    r.SetResult(j["result"]);
  }
}

void FromJson(const nlohmann::json& j, UpdateSegmentResponse& r) {
  if (j.contains("data")) {
    // r.SetData(j["data"].dump());
  }
}

void FromJson(const nlohmann::json& j, RetrieveDatasetResponse& r) {
  if (j.contains("query")) {
    r.SetQuery(j["query"].dump());
  }
  if (j.contains("records")) {
    r.SetRecords(j["records"].dump());
  }
}

}  // namespace amssdk