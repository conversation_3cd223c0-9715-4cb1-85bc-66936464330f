#ifndef AMSSDK_SERIALIZER_V2_ENUM_CONVERTER_H
#define AMSSDK_SERIALIZER_V2_ENUM_CONVERTER_H

#include <unordered_map>
#include <string>
#include <type_traits>

namespace amssdk::serializer::v2 {

// 枚举转换器基类
template<typename EnumType>
class EnumConverter {
    static_assert(std::is_enum_v<EnumType>, "EnumType must be an enum");
    
public:
    using EnumToStringMap = std::unordered_map<EnumType, std::string>;
    using StringToEnumMap = std::unordered_map<std::string, EnumType>;
    
    static void RegisterMapping(EnumType enum_value, const std::string& string_value) {
        GetEnumToStringMap()[enum_value] = string_value;
        GetStringToEnumMap()[string_value] = enum_value;
    }
    
    static std::string ToString(EnumType enum_value) {
        const auto& map = GetEnumToStringMap();
        auto it = map.find(enum_value);
        if (it != map.end()) {
            return it->second;
        }
        
        // 回退到数值转换
        return std::to_string(static_cast<std::underlying_type_t<EnumType>>(enum_value));
    }
    
    static EnumType FromString(const std::string& string_value) {
        const auto& map = GetStringToEnumMap();
        auto it = map.find(string_value);
        if (it != map.end()) {
            return it->second;
        }
        
        // 尝试数值转换
        try {
            auto numeric_value = std::stoll(string_value);
            return static_cast<EnumType>(numeric_value);
        } catch (...) {
            throw std::invalid_argument("Invalid enum string: " + string_value);
        }
    }
    
    static bool IsValidString(const std::string& string_value) {
        const auto& map = GetStringToEnumMap();
        return map.find(string_value) != map.end();
    }
    
    static std::vector<std::string> GetAllStrings() {
        std::vector<std::string> result;
        const auto& map = GetStringToEnumMap();
        result.reserve(map.size());
        
        for (const auto& pair : map) {
            result.push_back(pair.first);
        }
        return result;
    }
    
    static std::vector<EnumType> GetAllEnums() {
        std::vector<EnumType> result;
        const auto& map = GetEnumToStringMap();
        result.reserve(map.size());
        
        for (const auto& pair : map) {
            result.push_back(pair.first);
        }
        return result;
    }

private:
    static EnumToStringMap& GetEnumToStringMap() {
        static EnumToStringMap map;
        return map;
    }
    
    static StringToEnumMap& GetStringToEnumMap() {
        static StringToEnumMap map;
        return map;
    }
};

// 便利宏用于注册枚举映射
#define REGISTER_ENUM_MAPPING(EnumType, EnumValue, StringValue) \
    static bool enum_registered_##EnumType##_##EnumValue = \
        (EnumConverter<EnumType>::RegisterMapping(EnumType::EnumValue, StringValue), true);

// 自动注册枚举的宏
#define AUTO_REGISTER_ENUM(EnumType) \
    template<> \
    struct EnumRegistrar<EnumType> { \
        static void Register(); \
        static inline bool registered = (Register(), true); \
    };

// 特化的枚举转换器
template<typename EnumType>
struct EnumRegistrar {
    static void Register() {
        // 默认实现：什么都不做
        // 用户需要特化这个模板来注册枚举映射
    }
};

// 现有枚举类型的转换器
class ResponseModeConverter : public EnumConverter<ResponseMode> {
public:
    static void RegisterMappings() {
        RegisterMapping(ResponseMode::STREAMING, "streaming");
        RegisterMapping(ResponseMode::BLOCKING, "blocking");
    }
    
private:
    static inline bool registered_ = (RegisterMappings(), true);
};

class FileTypeConverter : public EnumConverter<FileAttachment::FileType> {
public:
    static void RegisterMappings() {
        RegisterMapping(FileAttachment::FileType::IMAGE, "image");
        RegisterMapping(FileAttachment::FileType::DOCUMENT, "document");
        RegisterMapping(FileAttachment::FileType::VIDEO, "video");
        RegisterMapping(FileAttachment::FileType::AUDIO, "audio");
    }
    
private:
    static inline bool registered_ = (RegisterMappings(), true);
};

class TransferMethodConverter : public EnumConverter<FileAttachment::TransferMethod> {
public:
    static void RegisterMappings() {
        RegisterMapping(FileAttachment::TransferMethod::LOCAL_FILE, "local_file");
        RegisterMapping(FileAttachment::TransferMethod::REMOTE_URL, "remote_url");
    }
    
private:
    static inline bool registered_ = (RegisterMappings(), true);
};

class SortRuleConverter : public EnumConverter<SortRule> {
public:
    static void RegisterMappings() {
        RegisterMapping(SortRule::CREATED_AT, "created_at");
        RegisterMapping(SortRule::UPDATED_AT, "updated_at");
        RegisterMapping(SortRule::NAME, "name");
    }
    
private:
    static inline bool registered_ = (RegisterMappings(), true);
};

class ProcessRuleModeConverter : public EnumConverter<ProcessRuleMode> {
public:
    static void RegisterMappings() {
        RegisterMapping(ProcessRuleMode::AUTOMATIC, "automatic");
        RegisterMapping(ProcessRuleMode::CUSTOM, "custom");
    }
    
private:
    static inline bool registered_ = (RegisterMappings(), true);
};

class IndexingTechniqueConverter : public EnumConverter<IndexingTechnique> {
public:
    static void RegisterMappings() {
        RegisterMapping(IndexingTechnique::HIGH_QUALITY, "high_quality");
        RegisterMapping(IndexingTechnique::ECONOMY, "economy");
    }
    
private:
    static inline bool registered_ = (RegisterMappings(), true);
};

class CreateDatasetPermissionConverter : public EnumConverter<CreateDatasetPermission> {
public:
    static void RegisterMappings() {
        RegisterMapping(CreateDatasetPermission::ONLY_ME, "only_me");
        RegisterMapping(CreateDatasetPermission::ALL_TEAM_MEMBERS, "all_team_members");
        RegisterMapping(CreateDatasetPermission::PARTIAL_MEMBERS, "partial_members");
    }
    
private:
    static inline bool registered_ = (RegisterMappings(), true);
};

class CreateDatasetProviderConverter : public EnumConverter<CreateDatasetProvider> {
public:
    static void RegisterMappings() {
        RegisterMapping(CreateDatasetProvider::VENDOR, "vendor");
        RegisterMapping(CreateDatasetProvider::HOSTED, "hosted");
    }
    
private:
    static inline bool registered_ = (RegisterMappings(), true);
};

class SearchMethodConverter : public EnumConverter<SearchMethod> {
public:
    static void RegisterMappings() {
        RegisterMapping(SearchMethod::SEMANTIC_SEARCH, "semantic_search");
        RegisterMapping(SearchMethod::FULL_TEXT_SEARCH, "full_text_search");
        RegisterMapping(SearchMethod::HYBRID_SEARCH, "hybrid_search");
    }
    
private:
    static inline bool registered_ = (RegisterMappings(), true);
};

} // namespace amssdk::serializer::v2

#endif // AMSSDK_SERIALIZER_V2_ENUM_CONVERTER_H
