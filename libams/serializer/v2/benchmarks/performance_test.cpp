#include "../core/serializer.h"
#include "../converters/enum_converter.h"
#include "include/chat/chat_request.h"
#include <chrono>
#include <vector>
#include <iostream>
#include <fstream>
#include <random>

using namespace amssdk::serializer::v2;

// 注册测试类型
BEGIN_SERIALIZATION(ChatRequest)
    FIELD(inputs)
    FIELD(query)
    FIELD_WITH_CONVERTER(response_mode, ResponseModeConverter)
    FIELD(conversation_id)
    FIELD(user)
END_SERIALIZATION(ChatRequest)

class PerformanceBenchmark {
public:
    struct BenchmarkResult {
        std::string test_name;
        size_t object_count;
        std::chrono::microseconds duration;
        size_t memory_used;
        double objects_per_second;
        double mb_per_second;
    };
    
    PerformanceBenchmark() : gen_(std::random_device{}()) {}
    
    void RunAllBenchmarks() {
        std::cout << "=== 序列化性能基准测试 ===" << std::endl;
        
        // 不同规模的测试
        std::vector<size_t> test_sizes = {100, 1000, 10000, 100000};
        
        for (size_t size : test_sizes) {
            std::cout << "\n--- 测试规模: " << size << " 对象 ---" << std::endl;
            
            auto objects = GenerateTestObjects(size);
            
            // 单线程序列化测试
            auto serialize_result = BenchmarkSerialization(objects);
            PrintResult(serialize_result);
            
            // 批量序列化测试
            auto batch_serialize_result = BenchmarkBatchSerialization(objects);
            PrintResult(batch_serialize_result);
            
            // 反序列化测试
            auto json_strings = SerializeObjects(objects);
            auto deserialize_result = BenchmarkDeserialization(json_strings);
            PrintResult(deserialize_result);
            
            // 批量反序列化测试
            auto batch_deserialize_result = BenchmarkBatchDeserialization(json_strings);
            PrintResult(batch_deserialize_result);
            
            // 内存使用测试
            auto memory_result = BenchmarkMemoryUsage(objects);
            PrintResult(memory_result);
        }
        
        // 特殊测试
        std::cout << "\n=== 特殊性能测试 ===" << std::endl;
        BenchmarkStreamingPerformance();
        BenchmarkErrorHandlingPerformance();
        BenchmarkEnumConversionPerformance();
    }

private:
    std::mt19937 gen_;
    
    std::vector<ChatRequest> GenerateTestObjects(size_t count) {
        std::vector<ChatRequest> objects;
        objects.reserve(count);
        
        std::uniform_int_distribution<> mode_dist(0, 1);
        
        for (size_t i = 0; i < count; ++i) {
            ChatRequest req;
            req.SetQuery("Test query " + std::to_string(i) + " with some longer text to simulate real usage");
            req.SetUser("user_" + std::to_string(i % 1000));
            req.SetConversationId("conv_" + std::to_string(i % 100));
            req.SetResponseMode(mode_dist(gen_) ? ResponseMode::STREAMING : ResponseMode::BLOCKING);
            
            // 添加一些输入数据
            std::string inputs = R"({"param1": "value)" + std::to_string(i) + R"(", "param2": )" + std::to_string(i * 2) + "}";
            req.SetInputs(inputs);
            
            objects.push_back(req);
        }
        
        return objects;
    }
    
    std::vector<std::string> SerializeObjects(const std::vector<ChatRequest>& objects) {
        std::vector<std::string> json_strings;
        json_strings.reserve(objects.size());
        
        Serializer serializer;
        for (const auto& obj : objects) {
            auto result = serializer.ToJson(obj);
            if (result) {
                json_strings.push_back(result.data);
            }
        }
        
        return json_strings;
    }
    
    BenchmarkResult BenchmarkSerialization(const std::vector<ChatRequest>& objects) {
        Serializer serializer;
        
        auto start = std::chrono::high_resolution_clock::now();
        
        size_t total_bytes = 0;
        for (const auto& obj : objects) {
            auto result = serializer.ToJson(obj);
            if (result) {
                total_bytes += result.data.size();
            }
        }
        
        auto end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
        
        double objects_per_second = objects.size() * 1000000.0 / duration.count();
        double mb_per_second = total_bytes * 1000000.0 / (duration.count() * 1024 * 1024);
        
        return {
            "单线程序列化",
            objects.size(),
            duration,
            total_bytes,
            objects_per_second,
            mb_per_second
        };
    }
    
    BenchmarkResult BenchmarkBatchSerialization(const std::vector<ChatRequest>& objects) {
        Serializer serializer;
        
        auto start = std::chrono::high_resolution_clock::now();
        
        auto result = serializer.ToJsonBatch(objects);
        
        auto end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
        
        size_t total_bytes = 0;
        if (result) {
            for (const auto& json_str : result.data) {
                total_bytes += json_str.size();
            }
        }
        
        double objects_per_second = objects.size() * 1000000.0 / duration.count();
        double mb_per_second = total_bytes * 1000000.0 / (duration.count() * 1024 * 1024);
        
        return {
            "批量序列化",
            objects.size(),
            duration,
            total_bytes,
            objects_per_second,
            mb_per_second
        };
    }
    
    BenchmarkResult BenchmarkDeserialization(const std::vector<std::string>& json_strings) {
        Serializer serializer;
        
        auto start = std::chrono::high_resolution_clock::now();
        
        size_t success_count = 0;
        size_t total_bytes = 0;
        for (const auto& json_str : json_strings) {
            auto result = serializer.FromJson<ChatRequest>(json_str);
            if (result) {
                success_count++;
                total_bytes += json_str.size();
            }
        }
        
        auto end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
        
        double objects_per_second = success_count * 1000000.0 / duration.count();
        double mb_per_second = total_bytes * 1000000.0 / (duration.count() * 1024 * 1024);
        
        return {
            "单线程反序列化",
            success_count,
            duration,
            total_bytes,
            objects_per_second,
            mb_per_second
        };
    }
    
    BenchmarkResult BenchmarkBatchDeserialization(const std::vector<std::string>& json_strings) {
        Serializer serializer;
        
        auto start = std::chrono::high_resolution_clock::now();
        
        auto result = serializer.FromJsonBatch<ChatRequest>(json_strings);
        
        auto end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
        
        size_t total_bytes = 0;
        for (const auto& json_str : json_strings) {
            total_bytes += json_str.size();
        }
        
        size_t success_count = result ? result.data.size() : 0;
        double objects_per_second = success_count * 1000000.0 / duration.count();
        double mb_per_second = total_bytes * 1000000.0 / (duration.count() * 1024 * 1024);
        
        return {
            "批量反序列化",
            success_count,
            duration,
            total_bytes,
            objects_per_second,
            mb_per_second
        };
    }
    
    BenchmarkResult BenchmarkMemoryUsage(const std::vector<ChatRequest>& objects) {
        Serializer::Options options;
        Serializer serializer(options);
        
        // 测量内存使用
        auto start_memory = GetMemoryUsage();
        
        auto start = std::chrono::high_resolution_clock::now();
        
        auto result = serializer.ToJsonBatch(objects);
        
        auto end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
        
        auto end_memory = GetMemoryUsage();
        size_t memory_used = end_memory - start_memory;
        
        return {
            "内存使用测试",
            objects.size(),
            duration,
            memory_used,
            0.0,
            0.0
        };
    }
    
    void BenchmarkStreamingPerformance() {
        auto objects = GenerateTestObjects(1000);
        
        // 流式序列化到文件
        auto start = std::chrono::high_resolution_clock::now();
        
        std::ofstream file("benchmark_output.json");
        Serializer serializer;
        
        for (const auto& obj : objects) {
            serializer.ToJsonStream(obj, file);
            file << "\n";
        }
        file.close();
        
        auto end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
        
        std::cout << "流式序列化: " << objects.size() << " 对象, "
                  << duration.count() << " 微秒" << std::endl;
        
        // 清理
        std::remove("benchmark_output.json");
    }
    
    void BenchmarkErrorHandlingPerformance() {
        // 测试错误处理的性能影响
        std::vector<std::string> invalid_jsons = {
            R"({"invalid": "json", "missing_fields": true})",
            R"({"query": null, "user": ""})",
            R"(malformed json)",
            R"({"query": "test"})"  // 缺少必需字段
        };
        
        Serializer::Options options;
        options.error_handler = std::make_shared<DefaultErrorHandler>();
        Serializer serializer(options);
        
        auto start = std::chrono::high_resolution_clock::now();
        
        for (int i = 0; i < 1000; ++i) {
            for (const auto& json : invalid_jsons) {
                auto result = serializer.FromJson<ChatRequest>(json);
                // 忽略结果，只测试性能
            }
        }
        
        auto end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
        
        std::cout << "错误处理性能: " << invalid_jsons.size() * 1000 << " 次失败操作, "
                  << duration.count() << " 微秒" << std::endl;
    }
    
    void BenchmarkEnumConversionPerformance() {
        const size_t iterations = 100000;
        
        auto start = std::chrono::high_resolution_clock::now();
        
        for (size_t i = 0; i < iterations; ++i) {
            auto mode = (i % 2) ? ResponseMode::STREAMING : ResponseMode::BLOCKING;
            auto str = ResponseModeConverter::ToString(mode);
            auto back = ResponseModeConverter::FromString(str);
            (void)back; // 避免未使用变量警告
        }
        
        auto end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
        
        std::cout << "枚举转换性能: " << iterations << " 次转换, "
                  << duration.count() << " 微秒" << std::endl;
    }
    
    void PrintResult(const BenchmarkResult& result) {
        std::cout << result.test_name << ":" << std::endl;
        std::cout << "  对象数量: " << result.object_count << std::endl;
        std::cout << "  耗时: " << result.duration.count() << " 微秒" << std::endl;
        std::cout << "  内存使用: " << result.memory_used / 1024.0 << " KB" << std::endl;
        
        if (result.objects_per_second > 0) {
            std::cout << "  处理速度: " << static_cast<int>(result.objects_per_second) << " 对象/秒" << std::endl;
            std::cout << "  吞吐量: " << result.mb_per_second << " MB/秒" << std::endl;
        }
        std::cout << std::endl;
    }
    
    size_t GetMemoryUsage() {
        // 简化的内存使用测量
        // 在实际实现中，可以使用更精确的方法
        return 0;
    }
};

int main() {
    PerformanceBenchmark benchmark;
    benchmark.RunAllBenchmarks();
    
    return 0;
}
