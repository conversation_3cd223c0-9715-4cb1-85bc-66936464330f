#include "../core/serializer.h"
#include "../converters/enum_converter.h"
#include "include/chat/chat_request.h"
#include "include/common/common.h"

using namespace amssdk::serializer::v2;

// 示例：为ChatRequest注册序列化
BEGIN_SERIALIZATION(ChatRequest)
    FIELD(inputs)
    FIELD(query)
    FIELD_WITH_CONVERTER(response_mode, ResponseModeConverter)
    FIELD(conversation_id)
    FIELD(user)
    FIELD_OPTIONAL(auto_generate_name)
END_SERIALIZATION(ChatRequest)

// 示例：为WorkflowRunRequest注册序列化
BEGIN_SERIALIZATION(WorkflowRunRequest)
    FIELD(user)
    FIELD(inputs)
    FIELD_WITH_CONVERTER(response_mode, ResponseModeConverter)
    FIELD_OPTIONAL(files)
END_SERIALIZATION(WorkflowRunRequest)

void BasicUsageExample() {
    // 创建请求对象
    ChatRequest request;
    request.SetQuery("Hello, world!");
    request.SetUser("test_user");
    request.SetResponseMode(ResponseMode::STREAMING);
    request.SetConversationId("conv_123");
    
    // 序列化
    auto result = ToJson(request);
    if (result) {
        std::cout << "Serialized JSON: " << result.data << std::endl;
    } else {
        std::cout << "Serialization failed: " << result.error_message << std::endl;
    }
    
    // 反序列化
    auto deserialized = FromJson<ChatRequest>(result.data);
    if (deserialized) {
        std::cout << "Deserialized query: " << deserialized.data.GetQuery() << std::endl;
    } else {
        std::cout << "Deserialization failed: " << deserialized.error_message << std::endl;
    }
}

void AdvancedUsageExample() {
    // 配置序列化选项
    Serializer::Options options;
    options.pretty_print = true;
    options.skip_null_values = true;
    options.validate_required_fields = true;
    options.error_handler = std::make_shared<LoggingErrorHandler>([](const std::string& msg) {
        std::cout << "LOG: " << msg << std::endl;
    });
    
    Serializer serializer(options);
    
    // 创建多个请求对象
    std::vector<ChatRequest> requests;
    for (int i = 0; i < 3; ++i) {
        ChatRequest req;
        req.SetQuery("Query " + std::to_string(i));
        req.SetUser("user_" + std::to_string(i));
        req.SetResponseMode(ResponseMode::BLOCKING);
        requests.push_back(req);
    }
    
    // 批量序列化
    auto batch_result = serializer.ToJsonBatch(requests);
    if (batch_result) {
        std::cout << "Batch serialization successful, " 
                  << batch_result.data.size() << " objects processed" << std::endl;
        
        // 批量反序列化
        auto batch_deserialized = serializer.FromJsonBatch<ChatRequest>(batch_result.data);
        if (batch_deserialized) {
            std::cout << "Batch deserialization successful, " 
                      << batch_deserialized.data.size() << " objects processed" << std::endl;
        }
    }
}

void PerformanceExample() {
    // 创建大量对象进行性能测试
    const size_t num_objects = 10000;
    std::vector<ChatRequest> requests;
    requests.reserve(num_objects);
    
    for (size_t i = 0; i < num_objects; ++i) {
        ChatRequest req;
        req.SetQuery("Performance test query " + std::to_string(i));
        req.SetUser("perf_user");
        req.SetResponseMode(ResponseMode::STREAMING);
        requests.push_back(req);
    }
    
    // 配置高性能选项
    Serializer::Options options;
    options.pretty_print = false;  // 关闭格式化以提高性能
    options.skip_null_values = true;
    
    Serializer serializer(options);
    
    auto start = std::chrono::high_resolution_clock::now();
    
    // 批量序列化
    auto result = serializer.ToJsonBatch(requests);
    
    auto end = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
    
    if (result) {
        std::cout << "Serialized " << num_objects << " objects in " 
                  << duration.count() << "ms" << std::endl;
        
        // 获取统计信息
        const auto& stats = serializer.GetStats();
        std::cout << "Average serialize time: " << stats.GetAverageSerializeTime() 
                  << " microseconds" << std::endl;
        std::cout << "Total bytes serialized: " << stats.bytes_serialized << std::endl;
    }
}

void ErrorHandlingExample() {
    // 创建组合错误处理器
    auto composite_handler = std::make_shared<CompositeErrorHandler>();
    
    // 添加日志处理器
    auto logging_handler = std::make_shared<LoggingErrorHandler>([](const std::string& msg) {
        std::cout << "[ERROR LOG] " << msg << std::endl;
    });
    composite_handler->AddHandler(logging_handler);
    
    // 添加默认处理器
    auto default_handler = std::make_shared<DefaultErrorHandler>();
    composite_handler->AddHandler(default_handler);
    
    Serializer::Options options;
    options.error_handler = composite_handler;
    options.validate_required_fields = true;
    
    Serializer serializer(options);
    
    // 尝试反序列化无效的JSON
    std::string invalid_json = R"({"query": "test", "missing_required_field": true})";
    
    auto result = serializer.FromJson<ChatRequest>(invalid_json);
    if (!result) {
        std::cout << "Expected failure: " << result.error_message << std::endl;
        
        // 检查详细错误信息
        auto errors = composite_handler->GetErrors();
        for (const auto& error : errors) {
            std::cout << "Error in field '" << error.field_name 
                      << "': " << error.message << std::endl;
        }
    }
}

void StreamingExample() {
    ChatRequest request;
    request.SetQuery("Streaming test");
    request.SetUser("stream_user");
    request.SetResponseMode(ResponseMode::STREAMING);
    
    Serializer serializer;
    
    // 流式序列化到文件
    std::ofstream file("test_output.json");
    if (serializer.ToJsonStream(request, file)) {
        std::cout << "Successfully serialized to stream" << std::endl;
    }
    file.close();
    
    // 流式反序列化从文件
    std::ifstream input_file("test_output.json");
    auto result = serializer.FromJsonStream<ChatRequest>(input_file);
    if (result) {
        std::cout << "Successfully deserialized from stream: " 
                  << result.data.GetQuery() << std::endl;
    }
    input_file.close();
}

int main() {
    std::cout << "=== Basic Usage Example ===" << std::endl;
    BasicUsageExample();
    
    std::cout << "\n=== Advanced Usage Example ===" << std::endl;
    AdvancedUsageExample();
    
    std::cout << "\n=== Performance Example ===" << std::endl;
    PerformanceExample();
    
    std::cout << "\n=== Error Handling Example ===" << std::endl;
    ErrorHandlingExample();
    
    std::cout << "\n=== Streaming Example ===" << std::endl;
    StreamingExample();
    
    return 0;
}
