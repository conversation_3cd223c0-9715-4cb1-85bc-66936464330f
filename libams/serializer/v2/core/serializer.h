#ifndef AMSSDK_SERIALIZER_V2_H
#define AMSSDK_SERIALIZER_V2_H

#include <nlohmann/json.hpp>
#include <string>
#include <type_traits>
#include <memory>
#include <unordered_map>
#include <functional>

namespace amssdk::serializer::v2 {

// 前向声明
class SerializationContext;
class ErrorHandler;

// 序列化结果
template<typename T>
struct SerializationResult {
    bool success = false;
    T data;
    std::string error_message;
    
    explicit operator bool() const { return success; }
    
    static SerializationResult Success(T&& value) {
        return {true, std::move(value), ""};
    }
    
    static SerializationResult Error(const std::string& error) {
        return {false, T{}, error};
    }
};

// 字段描述符
struct FieldDescriptor {
    std::string name;
    std::function<void(nlohmann::json&, const void*)> serializer;
    std::function<void(const nlohmann::json&, void*)> deserializer;
    bool required = true;
    bool skip_if_empty = false;
};

// 类型注册表
class TypeRegistry {
public:
    template<typename T>
    static void RegisterType(const std::vector<FieldDescriptor>& fields);
    
    template<typename T>
    static const std::vector<FieldDescriptor>* GetFields();
    
private:
    static std::unordered_map<std::type_index, std::vector<FieldDescriptor>> registry_;
};

// 主序列化器类
class Serializer {
public:
    // 配置选项
    struct Options {
        bool pretty_print = false;
        bool skip_null_values = true;
        bool validate_required_fields = true;
        size_t max_depth = 100;
        std::shared_ptr<ErrorHandler> error_handler;
    };
    
    explicit Serializer(const Options& options = {});
    
    // 序列化到JSON
    template<typename T>
    SerializationResult<std::string> ToJson(const T& obj);
    
    template<typename T>
    SerializationResult<nlohmann::json> ToJsonObject(const T& obj);
    
    // 从JSON反序列化
    template<typename T>
    SerializationResult<T> FromJson(const std::string& json_str);
    
    template<typename T>
    SerializationResult<T> FromJsonObject(const nlohmann::json& json_obj);
    
    // 批量操作
    template<typename T>
    SerializationResult<std::vector<std::string>> ToJsonBatch(const std::vector<T>& objects);
    
    template<typename T>
    SerializationResult<std::vector<T>> FromJsonBatch(const std::vector<std::string>& json_strings);
    
    // 流式操作
    template<typename T>
    bool ToJsonStream(const T& obj, std::ostream& stream);
    
    template<typename T>
    SerializationResult<T> FromJsonStream(std::istream& stream);

private:
    Options options_;
    std::unique_ptr<SerializationContext> context_;
    
    template<typename T>
    void SerializeObject(nlohmann::json& json, const T& obj);
    
    template<typename T>
    void DeserializeObject(const nlohmann::json& json, T& obj);
};

// 便利函数
template<typename T>
SerializationResult<std::string> ToJson(const T& obj, const Serializer::Options& options = {}) {
    Serializer serializer(options);
    return serializer.ToJson(obj);
}

template<typename T>
SerializationResult<T> FromJson(const std::string& json_str, const Serializer::Options& options = {}) {
    Serializer serializer(options);
    return serializer.FromJson<T>(json_str);
}

} // namespace amssdk::serializer::v2

// 注册宏
#define SERIALIZABLE_CLASS(ClassName) \
    friend class ::amssdk::serializer::v2::Serializer; \
    static void RegisterSerialization(); \
    static inline bool serialization_registered_ = (RegisterSerialization(), true);

#define BEGIN_SERIALIZATION(ClassName) \
    void ClassName::RegisterSerialization() { \
        using namespace ::amssdk::serializer::v2; \
        std::vector<FieldDescriptor> fields;

#define FIELD(field_name) \
    fields.emplace_back(FieldDescriptor{ \
        #field_name, \
        [](nlohmann::json& j, const void* obj) { \
            const auto* typed_obj = static_cast<const std::remove_reference_t<decltype(*this)>*>(obj); \
            j[#field_name] = typed_obj->field_name; \
        }, \
        [](const nlohmann::json& j, void* obj) { \
            auto* typed_obj = static_cast<std::remove_reference_t<decltype(*this)>*>(obj); \
            if (j.contains(#field_name)) { \
                typed_obj->field_name = j[#field_name]; \
            } \
        } \
    });

#define FIELD_OPTIONAL(field_name) \
    fields.emplace_back(FieldDescriptor{ \
        #field_name, \
        [](nlohmann::json& j, const void* obj) { \
            const auto* typed_obj = static_cast<const std::remove_reference_t<decltype(*this)>*>(obj); \
            j[#field_name] = typed_obj->field_name; \
        }, \
        [](const nlohmann::json& j, void* obj) { \
            auto* typed_obj = static_cast<std::remove_reference_t<decltype(*this)>*>(obj); \
            if (j.contains(#field_name)) { \
                typed_obj->field_name = j[#field_name]; \
            } \
        }, \
        false \
    });

#define END_SERIALIZATION(ClassName) \
        TypeRegistry::RegisterType<ClassName>(fields); \
    }

#include "serializer_impl.h"

#endif // AMSSDK_SERIALIZER_V2_H
