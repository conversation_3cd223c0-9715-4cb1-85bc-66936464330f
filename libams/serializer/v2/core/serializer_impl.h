#ifndef AMSSDK_SERIALIZER_V2_IMPL_H
#define AMSSDK_SERIALIZER_V2_IMPL_H

#include "serializer.h"
#include "context.h"
#include "error_handler.h"
#include <typeindex>

namespace amssdk::serializer::v2 {

// TypeRegistry 实现
std::unordered_map<std::type_index, std::vector<FieldDescriptor>> TypeRegistry::registry_;

template<typename T>
void TypeRegistry::RegisterType(const std::vector<FieldDescriptor>& fields) {
    registry_[std::type_index(typeid(T))] = fields;
}

template<typename T>
const std::vector<FieldDescriptor>* TypeRegistry::GetFields() {
    auto it = registry_.find(std::type_index(typeid(T)));
    return it != registry_.end() ? &it->second : nullptr;
}

// Serializer 实现
inline Serializer::Serializer(const Options& options) 
    : options_(options), context_(std::make_unique<SerializationContext>()) {
    if (!options_.error_handler) {
        options_.error_handler = std::make_shared<DefaultErrorHandler>();
    }
}

template<typename T>
SerializationResult<std::string> Serializer::ToJson(const T& obj) {
    try {
        nlohmann::json json_obj;
        SerializeObject(json_obj, obj);
        
        std::string result = options_.pretty_print ? 
            json_obj.dump(2) : json_obj.dump();
            
        return SerializationResult<std::string>::Success(std::move(result));
    } catch (const std::exception& e) {
        return SerializationResult<std::string>::Error(
            "Serialization failed: " + std::string(e.what()));
    }
}

template<typename T>
SerializationResult<nlohmann::json> Serializer::ToJsonObject(const T& obj) {
    try {
        nlohmann::json json_obj;
        SerializeObject(json_obj, obj);
        return SerializationResult<nlohmann::json>::Success(std::move(json_obj));
    } catch (const std::exception& e) {
        return SerializationResult<nlohmann::json>::Error(
            "Serialization failed: " + std::string(e.what()));
    }
}

template<typename T>
SerializationResult<T> Serializer::FromJson(const std::string& json_str) {
    try {
        nlohmann::json json_obj = nlohmann::json::parse(json_str);
        return FromJsonObject<T>(json_obj);
    } catch (const nlohmann::json::parse_error& e) {
        return SerializationResult<T>::Error(
            "JSON parse error: " + std::string(e.what()));
    }
}

template<typename T>
SerializationResult<T> Serializer::FromJsonObject(const nlohmann::json& json_obj) {
    try {
        T result;
        DeserializeObject(json_obj, result);
        return SerializationResult<T>::Success(std::move(result));
    } catch (const std::exception& e) {
        return SerializationResult<T>::Error(
            "Deserialization failed: " + std::string(e.what()));
    }
}

template<typename T>
SerializationResult<std::vector<std::string>> Serializer::ToJsonBatch(const std::vector<T>& objects) {
    std::vector<std::string> results;
    results.reserve(objects.size());
    
    for (const auto& obj : objects) {
        auto result = ToJson(obj);
        if (!result) {
            return SerializationResult<std::vector<std::string>>::Error(result.error_message);
        }
        results.push_back(std::move(result.data));
    }
    
    return SerializationResult<std::vector<std::string>>::Success(std::move(results));
}

template<typename T>
SerializationResult<std::vector<T>> Serializer::FromJsonBatch(const std::vector<std::string>& json_strings) {
    std::vector<T> results;
    results.reserve(json_strings.size());
    
    for (const auto& json_str : json_strings) {
        auto result = FromJson<T>(json_str);
        if (!result) {
            return SerializationResult<std::vector<T>>::Error(result.error_message);
        }
        results.push_back(std::move(result.data));
    }
    
    return SerializationResult<std::vector<T>>::Success(std::move(results));
}

template<typename T>
bool Serializer::ToJsonStream(const T& obj, std::ostream& stream) {
    try {
        auto result = ToJson(obj);
        if (result) {
            stream << result.data;
            return true;
        }
        return false;
    } catch (...) {
        return false;
    }
}

template<typename T>
SerializationResult<T> Serializer::FromJsonStream(std::istream& stream) {
    try {
        nlohmann::json json_obj;
        stream >> json_obj;
        return FromJsonObject<T>(json_obj);
    } catch (const std::exception& e) {
        return SerializationResult<T>::Error(
            "Stream deserialization failed: " + std::string(e.what()));
    }
}

template<typename T>
void Serializer::SerializeObject(nlohmann::json& json, const T& obj) {
    const auto* fields = TypeRegistry::GetFields<T>();
    if (!fields) {
        throw std::runtime_error("Type not registered for serialization: " + 
                                std::string(typeid(T).name()));
    }
    
    json = nlohmann::json::object();
    
    for (const auto& field : *fields) {
        try {
            nlohmann::json field_json;
            field.serializer(field_json, &obj);
            
            if (!options_.skip_null_values || !field_json.is_null()) {
                json[field.name] = std::move(field_json);
            }
        } catch (const std::exception& e) {
            options_.error_handler->HandleFieldError(field.name, e.what());
        }
    }
}

template<typename T>
void Serializer::DeserializeObject(const nlohmann::json& json, T& obj) {
    const auto* fields = TypeRegistry::GetFields<T>();
    if (!fields) {
        throw std::runtime_error("Type not registered for deserialization: " + 
                                std::string(typeid(T).name()));
    }
    
    for (const auto& field : *fields) {
        try {
            if (field.required && !json.contains(field.name)) {
                if (options_.validate_required_fields) {
                    throw std::runtime_error("Required field missing: " + field.name);
                }
                continue;
            }
            
            if (json.contains(field.name)) {
                field.deserializer(json, &obj);
            }
        } catch (const std::exception& e) {
            options_.error_handler->HandleFieldError(field.name, e.what());
        }
    }
}

} // namespace amssdk::serializer::v2

#endif // AMSSDK_SERIALIZER_V2_IMPL_H
