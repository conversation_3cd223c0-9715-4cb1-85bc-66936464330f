#ifndef AMSSDK_SERIALIZER_V2_ERROR_HANDLER_H
#define AMSSDK_SERIALIZER_V2_ERROR_HANDLER_H

#include <string>
#include <vector>
#include <functional>
#include <memory>

namespace amssdk::serializer::v2 {

// 错误级别
enum class ErrorLevel {
    WARNING,
    ERROR,
    CRITICAL
};

// 错误信息
struct ErrorInfo {
    ErrorLevel level;
    std::string field_name;
    std::string message;
    std::string type_name;
    size_t line_number = 0;
    
    ErrorInfo(ErrorLevel lvl, const std::string& field, const std::string& msg, 
              const std::string& type = "", size_t line = 0)
        : level(lvl), field_name(field), message(msg), type_name(type), line_number(line) {}
};

// 错误处理器基类
class ErrorHandler {
public:
    virtual ~ErrorHandler() = default;
    
    virtual void HandleFieldError(const std::string& field_name, const std::string& error_message) = 0;
    virtual void HandleTypeError(const std::string& type_name, const std::string& error_message) = 0;
    virtual void HandleGeneralError(const std::string& error_message) = 0;
    
    virtual std::vector<ErrorInfo> GetErrors() const = 0;
    virtual void ClearErrors() = 0;
    virtual bool HasErrors() const = 0;
    virtual bool HasCriticalErrors() const = 0;
};

// 默认错误处理器
class DefaultErrorHandler : public ErrorHandler {
public:
    DefaultErrorHandler() = default;
    
    void HandleFieldError(const std::string& field_name, const std::string& error_message) override {
        errors_.emplace_back(ErrorLevel::ERROR, field_name, error_message);
    }
    
    void HandleTypeError(const std::string& type_name, const std::string& error_message) override {
        errors_.emplace_back(ErrorLevel::ERROR, "", error_message, type_name);
    }
    
    void HandleGeneralError(const std::string& error_message) override {
        errors_.emplace_back(ErrorLevel::ERROR, "", error_message);
    }
    
    std::vector<ErrorInfo> GetErrors() const override {
        return errors_;
    }
    
    void ClearErrors() override {
        errors_.clear();
    }
    
    bool HasErrors() const override {
        return !errors_.empty();
    }
    
    bool HasCriticalErrors() const override {
        for (const auto& error : errors_) {
            if (error.level == ErrorLevel::CRITICAL) {
                return true;
            }
        }
        return false;
    }

private:
    std::vector<ErrorInfo> errors_;
};

// 抛出异常的错误处理器
class ThrowingErrorHandler : public ErrorHandler {
public:
    void HandleFieldError(const std::string& field_name, const std::string& error_message) override {
        throw std::runtime_error("Field error in '" + field_name + "': " + error_message);
    }
    
    void HandleTypeError(const std::string& type_name, const std::string& error_message) override {
        throw std::runtime_error("Type error in '" + type_name + "': " + error_message);
    }
    
    void HandleGeneralError(const std::string& error_message) override {
        throw std::runtime_error("Serialization error: " + error_message);
    }
    
    std::vector<ErrorInfo> GetErrors() const override {
        return {}; // 不存储错误，直接抛出
    }
    
    void ClearErrors() override {
        // 无操作
    }
    
    bool HasErrors() const override {
        return false; // 有错误就抛出了
    }
    
    bool HasCriticalErrors() const override {
        return false;
    }
};

// 日志错误处理器
class LoggingErrorHandler : public ErrorHandler {
public:
    using LogFunction = std::function<void(const std::string&)>;
    
    explicit LoggingErrorHandler(LogFunction log_func) : log_function_(log_func) {}
    
    void HandleFieldError(const std::string& field_name, const std::string& error_message) override {
        std::string log_msg = "Field error in '" + field_name + "': " + error_message;
        log_function_(log_msg);
        errors_.emplace_back(ErrorLevel::ERROR, field_name, error_message);
    }
    
    void HandleTypeError(const std::string& type_name, const std::string& error_message) override {
        std::string log_msg = "Type error in '" + type_name + "': " + error_message;
        log_function_(log_msg);
        errors_.emplace_back(ErrorLevel::ERROR, "", error_message, type_name);
    }
    
    void HandleGeneralError(const std::string& error_message) override {
        std::string log_msg = "Serialization error: " + error_message;
        log_function_(log_msg);
        errors_.emplace_back(ErrorLevel::ERROR, "", error_message);
    }
    
    std::vector<ErrorInfo> GetErrors() const override {
        return errors_;
    }
    
    void ClearErrors() override {
        errors_.clear();
    }
    
    bool HasErrors() const override {
        return !errors_.empty();
    }
    
    bool HasCriticalErrors() const override {
        for (const auto& error : errors_) {
            if (error.level == ErrorLevel::CRITICAL) {
                return true;
            }
        }
        return false;
    }

private:
    LogFunction log_function_;
    std::vector<ErrorInfo> errors_;
};

// 组合错误处理器
class CompositeErrorHandler : public ErrorHandler {
public:
    void AddHandler(std::shared_ptr<ErrorHandler> handler) {
        handlers_.push_back(handler);
    }
    
    void HandleFieldError(const std::string& field_name, const std::string& error_message) override {
        for (auto& handler : handlers_) {
            handler->HandleFieldError(field_name, error_message);
        }
    }
    
    void HandleTypeError(const std::string& type_name, const std::string& error_message) override {
        for (auto& handler : handlers_) {
            handler->HandleTypeError(type_name, error_message);
        }
    }
    
    void HandleGeneralError(const std::string& error_message) override {
        for (auto& handler : handlers_) {
            handler->HandleGeneralError(error_message);
        }
    }
    
    std::vector<ErrorInfo> GetErrors() const override {
        std::vector<ErrorInfo> all_errors;
        for (const auto& handler : handlers_) {
            auto errors = handler->GetErrors();
            all_errors.insert(all_errors.end(), errors.begin(), errors.end());
        }
        return all_errors;
    }
    
    void ClearErrors() override {
        for (auto& handler : handlers_) {
            handler->ClearErrors();
        }
    }
    
    bool HasErrors() const override {
        for (const auto& handler : handlers_) {
            if (handler->HasErrors()) {
                return true;
            }
        }
        return false;
    }
    
    bool HasCriticalErrors() const override {
        for (const auto& handler : handlers_) {
            if (handler->HasCriticalErrors()) {
                return true;
            }
        }
        return false;
    }

private:
    std::vector<std::shared_ptr<ErrorHandler>> handlers_;
};

} // namespace amssdk::serializer::v2

#endif // AMSSDK_SERIALIZER_V2_ERROR_HANDLER_H
