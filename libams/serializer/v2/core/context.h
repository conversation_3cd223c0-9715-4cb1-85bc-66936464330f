#ifndef AMSSDK_SERIALIZER_V2_CONTEXT_H
#define AMSSDK_SERIALIZER_V2_CONTEXT_H

#include <memory>
#include <unordered_map>
#include <string>
#include <chrono>

namespace amssdk::serializer::v2 {

// 内存池管理器
class MemoryPool {
public:
    MemoryPool(size_t initial_size = 1024 * 1024); // 1MB
    ~MemoryPool();
    
    void* Allocate(size_t size);
    void Reset();
    
    size_t GetTotalAllocated() const { return total_allocated_; }
    size_t GetCurrentUsage() const { return current_usage_; }

private:
    struct Block {
        std::unique_ptr<char[]> data;
        size_t size;
        size_t used;
        
        Block(size_t block_size) : data(std::make_unique<char[]>(block_size)), 
                                   size(block_size), used(0) {}
    };
    
    std::vector<std::unique_ptr<Block>> blocks_;
    size_t total_allocated_;
    size_t current_usage_;
    size_t default_block_size_;
    
    Block* GetCurrentBlock();
    void AddNewBlock(size_t min_size);
};

// 序列化统计信息
struct SerializationStats {
    size_t objects_serialized = 0;
    size_t objects_deserialized = 0;
    size_t bytes_serialized = 0;
    size_t bytes_deserialized = 0;
    std::chrono::microseconds total_serialize_time{0};
    std::chrono::microseconds total_deserialize_time{0};
    size_t errors_count = 0;
    
    void Reset() {
        objects_serialized = 0;
        objects_deserialized = 0;
        bytes_serialized = 0;
        bytes_deserialized = 0;
        total_serialize_time = std::chrono::microseconds{0};
        total_deserialize_time = std::chrono::microseconds{0};
        errors_count = 0;
    }
    
    double GetAverageSerializeTime() const {
        return objects_serialized > 0 ? 
            static_cast<double>(total_serialize_time.count()) / objects_serialized : 0.0;
    }
    
    double GetAverageDeserializeTime() const {
        return objects_deserialized > 0 ? 
            static_cast<double>(total_deserialize_time.count()) / objects_deserialized : 0.0;
    }
};

// 序列化上下文
class SerializationContext {
public:
    SerializationContext();
    ~SerializationContext();
    
    // 内存管理
    MemoryPool& GetMemoryPool() { return *memory_pool_; }
    
    // 统计信息
    SerializationStats& GetStats() { return stats_; }
    const SerializationStats& GetStats() const { return stats_; }
    
    // 缓存管理
    void SetCacheEnabled(bool enabled) { cache_enabled_ = enabled; }
    bool IsCacheEnabled() const { return cache_enabled_; }
    
    template<typename T>
    void CacheResult(const std::string& key, const T& result);
    
    template<typename T>
    bool GetCachedResult(const std::string& key, T& result);
    
    void ClearCache();
    
    // 深度跟踪（防止循环引用）
    void PushDepth(const void* obj);
    void PopDepth();
    bool IsCircularReference(const void* obj) const;
    size_t GetCurrentDepth() const { return depth_stack_.size(); }
    
    // 性能监控
    class ScopedTimer {
    public:
        ScopedTimer(std::chrono::microseconds& target) 
            : target_(target), start_(std::chrono::high_resolution_clock::now()) {}
        
        ~ScopedTimer() {
            auto end = std::chrono::high_resolution_clock::now();
            target_ += std::chrono::duration_cast<std::chrono::microseconds>(end - start_);
        }
        
    private:
        std::chrono::microseconds& target_;
        std::chrono::high_resolution_clock::time_point start_;
    };
    
    ScopedTimer CreateSerializeTimer() {
        return ScopedTimer(stats_.total_serialize_time);
    }
    
    ScopedTimer CreateDeserializeTimer() {
        return ScopedTimer(stats_.total_deserialize_time);
    }

private:
    std::unique_ptr<MemoryPool> memory_pool_;
    SerializationStats stats_;
    bool cache_enabled_;
    std::unordered_map<std::string, std::string> cache_;
    std::vector<const void*> depth_stack_;
    static constexpr size_t MAX_DEPTH = 100;
};

// 模板实现
template<typename T>
void SerializationContext::CacheResult(const std::string& key, const T& result) {
    if (!cache_enabled_) return;
    
    // 这里可以实现更复杂的缓存策略
    // 暂时简单存储字符串表示
    cache_[key] = std::to_string(result);
}

template<typename T>
bool SerializationContext::GetCachedResult(const std::string& key, T& result) {
    if (!cache_enabled_) return false;
    
    auto it = cache_.find(key);
    if (it != cache_.end()) {
        // 这里需要实现从字符串到T的转换
        // 暂时返回false，表示未找到缓存
        return false;
    }
    return false;
}

} // namespace amssdk::serializer::v2

#endif // AMSSDK_SERIALIZER_V2_CONTEXT_H
