#ifndef AMSSDK_SERIALIZER_H
#define AMSSDK_SERIALIZER_H

#include <string>

namespace amssdk {
class WorkflowLogsRequest;
class WorkflowTaskStopRequest;
class WorkflowRunRequest;

class RenameConversationRequest;
class DeleteConversationRequest;
class ConversationRequest;
class MessagesRequest;
class SuggestedRequest;
class ChatRequest;
class TaskStopRequest;
class FeedbackRequest;
class CompletionMessageRequest;

// Knowledge base classes
class CreateDocumentByTextRequest;
class CreateDocumentByFileRequest;
class UpdateDocumentByTextRequest;
class UpdateDocumentByFileRequest;
class CreateDatasetRequest;
class ListDatasetsRequest;
class DeleteDatasetRequest;
class GetIndexingStatusRequest;
class DeleteDocumentRequest;
class ListDocumentsRequest;
class CreateSegmentRequest;
class ListSegmentsRequest;
class DeleteSegmentRequest;
class UpdateSegmentRequest;
class RetrieveDatasetRequest;

std::string SerializeChatRequest(const ChatRequest& request);
std::string SerializeTaskStopRequest(const TaskStopRequest& request);
std::string SerializeFeedbackRequest(const FeedbackRequest& request);
std::string SerializeDeleteConversationRequest(
    const DeleteConversationRequest& request);
std::string SerializeRenameConversationRequest(
    const RenameConversationRequest& request);
std::string SerializeSuggestedRequest(const SuggestedRequest& request);
std::string SerializeMessagesRequest(const MessagesRequest& request);
std::string SerializeConversationRequest(const ConversationRequest& request);
std::string SerializeChatCompletionRequest(
    const CompletionMessageRequest& request);
std::string SerializeWorkflowRunRequest(const WorkflowRunRequest& request);
std::string SerializeWorkflowTaskStopRequest(
    const WorkflowTaskStopRequest& request);
std::string SerializeWorkflowLogsRequest(const WorkflowLogsRequest& request);

// Knowledge base serialization functions
std::string SerializeCreateDocumentByTextRequest(
    const CreateDocumentByTextRequest& request);
std::string SerializeCreateDatasetRequest(const CreateDatasetRequest& request);
std::string SerializeListDatasetsRequest(const ListDatasetsRequest& request);
std::string SerializeDeleteDatasetRequest(const DeleteDatasetRequest& request);
std::string SerializeCreateDocumentByFileRequest(
    const CreateDocumentByFileRequest& request);
std::string SerializeUpdateDocumentByTextRequest(
    const UpdateDocumentByTextRequest& request);
std::string SerializeUpdateDocumentByFileRequest(
    const UpdateDocumentByFileRequest& request);
std::string SerializeGetIndexingStatusRequest(
    const GetIndexingStatusRequest& request);
std::string SerializeDeleteDocumentRequest(
    const DeleteDocumentRequest& request);
std::string SerializeListDocumentsRequest(const ListDocumentsRequest& request);
std::string SerializeCreateSegmentRequest(const CreateSegmentRequest& request);
std::string SerializeListSegmentsRequest(const ListSegmentsRequest& request);
std::string SerializeDeleteSegmentRequest(const DeleteSegmentRequest& request);
std::string SerializeUpdateSegmentRequest(const UpdateSegmentRequest& request);
std::string SerializeRetrieveDatasetRequest(
    const RetrieveDatasetRequest& request);

}  // namespace amssdk

#endif  //AMSSDK_SERIALIZER_H
