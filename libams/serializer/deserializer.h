#ifndef AMSSDK_DESERIALIZER_H
#define AMSSDK_DESERIALIZER_H

#include <nlohmann/json.hpp>

namespace amssdk {

class StreamEvent;
class FileResponse;
class SimpleResponse;
class ConversationResponse;
class SuggestedResponse;
class MessagesResponse;
class RenameConversationResponse;
class WorkflowRunResponse;
class WorkflowRunInfoResponse;
class WorkflowLogsResponse;

// Knowledge base classes
class CreateDocumentResponse;
class CreateDatasetResponse;
class ListDatasetsResponse;
class GetIndexingStatusResponse;
class DeleteDocumentResponse;
class ListDocumentsResponse;
class CreateSegmentResponse;
class ListSegmentsResponse;
class DeleteSegmentResponse;
class UpdateSegmentResponse;
class RetrieveDatasetResponse;

FileResponse DeserializeFileResponse(const nlohmann::json& j);
ConversationResponse DeserializeConversationResponse(const nlohmann::json& j);
SuggestedResponse DeserializeSuggestedResponse(const nlohmann::json& j);
MessagesResponse DeserializeMessagesResponse(const nlohmann::json& j);
SimpleResponse DeserializeDeleteConversationResponse(const nlohmann::json& j);
RenameConversationResponse DeserializeRenameConversationResponse(
    const nlohmann::json& j);
WorkflowRunResponse DeserializeWorkflowRunResponse(const nlohmann::json& j);
WorkflowRunInfoResponse DeserializeWorkflowRunInfoResponse(
    const nlohmann::json& j);
WorkflowLogsResponse DeserializeWorkflowLogsResponse(const nlohmann::json& j);

// Knowledge base deserialization functions
CreateDocumentResponse DeserializeCreateDocumentResponse(
    const nlohmann::json& j);
CreateDatasetResponse DeserializeCreateDatasetResponse(const nlohmann::json& j);
ListDatasetsResponse DeserializeListDatasetsResponse(const nlohmann::json& j);
GetIndexingStatusResponse DeserializeGetIndexingStatusResponse(
    const nlohmann::json& j);
DeleteDocumentResponse DeserializeDeleteDocumentResponse(
    const nlohmann::json& j);
ListDocumentsResponse DeserializeListDocumentsResponse(const nlohmann::json& j);
CreateSegmentResponse DeserializeCreateSegmentResponse(const nlohmann::json& j);
ListSegmentsResponse DeserializeListSegmentsResponse(const nlohmann::json& j);
DeleteSegmentResponse DeserializeDeleteSegmentResponse(const nlohmann::json& j);
UpdateSegmentResponse DeserializeUpdateSegmentResponse(const nlohmann::json& j);
RetrieveDatasetResponse DeserializeRetrieveDatasetResponse(
    const nlohmann::json& j);

}  // namespace amssdk
#endif  //AMSSDK_DESERIALIZER_H
