#ifndef AMSSDK_WORKFLOW_SERVICE_H
#define AMSSDK_WORKFLOW_SERVICE_H
#include "include/common/api_result.h"
namespace amssdk {
class WorkflowLogsResponse;
class WorkflowLogsRequest;
class WorkflowTaskStopRequest;
class WorkflowRunInfoResponse;
class SimpleResponse;
}  // namespace amssdk
namespace amssdk {
class WorkflowRunInfoRequest;
class StreamEvent;
class WorkflowRunRequest;
class WorkflowRunResponse;
class ApiManager;
class HttpClient;
class Authorization;

class WorkflowService {
 public:
  using StreamEventCallback = std::function<void(std::unique_ptr<StreamEvent>)>;
  explicit WorkflowService(ApiManager& apiManager);

  ApiResult<WorkflowRunResponse> WorkflowRun(
      const WorkflowRunRequest& request,
      const StreamEventCallback& stream_event_callback) const;
  ApiResult<WorkflowRunInfoResponse> WorkflowRunInfo(
      const WorkflowRunInfoRequest& request) const;
  ApiResult<SimpleResponse> WorkflowTaskStop(
      const WorkflowTaskStopRequest& request) const;
  ApiResult<WorkflowLogsResponse> WorkflowLogs(
      const WorkflowLogsRequest& request) const;

 private:
  HttpClient& http_client_;
  Authorization& auth_;
};
}  // namespace amssdk
#endif  //AMSSDK_WORKFLOW_SERVICE_H
