#ifndef AMSSDK_TASK_SERVICE_H
#define AMSSDK_TASK_SERVICE_H

#include "include/common/api_result.h"
#include "network/response.h"

namespace amssdk {

class ApiManager;
class HttpClient;
class Authorization;
class SimpleResponse;
class TaskStopRequest;
class FeedbackRequest;

class TaskService {
 public:
  LIBAMS_EXPORT
  explicit TaskService(ApiManager& api_manager);

  LIBAMS_EXPORT
  ApiResult<SimpleResponse> StopTask(const TaskStopRequest& request) const;
  LIBAMS_EXPORT
  ApiResult<SimpleResponse> SendFeedback(const FeedbackRequest& request) const;

 private:
  HttpClient& http_client_;
  Authorization& auth_;
};

}  // namespace amssdk

#endif  // AMSSDK_TASK_SERVICE_H
