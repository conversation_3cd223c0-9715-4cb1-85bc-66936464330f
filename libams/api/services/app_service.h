#ifndef AMSSDK_APP_SERVICE_H
#define AMSSDK_APP_SERVICE_H

#include "include/common/api_result.h"

namespace amssdk {
class AppInfoResponse;
class AppParamResponse;
class AppParamRequest;
class AppInfoRequest;
class HttpClient;
class Authorization;
class ApiManager;
class AppMetaRequest;
class AppMetaResponse;

class AppService {
 public:
  explicit AppService(ApiManager& api_manager);
  ApiResult<AppMetaResponse> Meta(const AppMetaRequest& request) const;
  ApiResult<AppParamResponse> Parameters(const AppParamRequest& request) const;
  ApiResult<AppInfoResponse> Info(const AppInfoRequest& request) const;

 private:
  HttpClient& http_client_;
  Authorization& auth_;
};

}  // namespace amssdk

#endif  //AMSSDK_APP_SERVICE_H
