#include "file_service.h"
#include <nlohmann/json.hpp>
#include "api/api_manager.h"
#include "include/common/endpoints.h"
#include "include/file.h"
#include "serializer/deserializer.h"

namespace amssdk {
FileService::FileService(ApiManager& api_manager)
    : http_client_(api_manager.GetHttpClient()),
      auth_(api_manager.GetAuthorization()) {}

ApiResult<FileResponse> FileService::Upload(
    const std::experimental::filesystem::path& file,
    const std::string& user) const {

  auto response = http_client_.MultipartForm(
      endpoints::kFileUpload, {{"file", file.string()}, {"user", user}});
  auto result = BuildResult<FileResponse>(response);

  if (result.ok) {
    try {
      nlohmann::json j = nlohmann::json::parse(result.raw_body);
      result.data = DeserializeFileResponse(j);
    } catch (const std::exception& e) {
      result.ok = false;
      result.error.message =
          "Failed to deserialize FileResponse: " + std::string(e.what());
    }
  }
  return result;
}
}  // namespace amssdk
// namespace amssdk
