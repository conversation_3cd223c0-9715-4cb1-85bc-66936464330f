#include "audio_service.h"

#include "api/api_manager.h"
#include "include/audio.h"
#include "include/common/endpoints.h"

namespace amssdk {

AudioService::AudioService(ApiManager& api_manager)
    : http_client_(api_manager.GetHttpClient()),
      auth_(api_manager.GetAuthorization()) {}

ApiResult<TextToAudioResponse> AudioService::TextToAudio(
    const TextToAudioRequest& request) const {

  std::map<std::string, std::string> form_data;
  form_data["text"] = request.GetText();
  form_data["user"] = request.GetUser();
  form_data["message_id"] = request.GetMessageId();

  auto result = http_client_.MultipartForm(endpoints::kTextToAudio, form_data);
  auto response = BuildResult<TextToAudioResponse>(result);
  if (response.ok) {
    response.data.SetBytes(result.content);
  }
  return response;
}

ApiResult<AudioToTextResponse> AudioService::AudioToText(
    const AudioToTextRequest& request) const {

  std::map<std::string, std::string> form_data;
  form_data["file"] = request.GetFilePath().string();
  form_data["user"] = request.GetUser();

  auto result = http_client_.MultipartForm(endpoints::kAudioToText, form_data);
  auto response = BuildResult<AudioToTextResponse>(result);
  if (response.ok) {
    response.data.SetText(result.content);
  }
  return response;
}
}  // namespace amssdk