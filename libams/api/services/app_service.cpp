#include "app_service.h"

#include "api/api_manager.h"
#include "include/app.h"
#include "include/common/endpoints.h"

namespace amssdk {
AppService::AppService(ApiManager& api_manager)
    : http_client_(api_manager.GetHttpClient()),
      auth_(api_manager.GetAuthorization()) {}

ApiResult<AppMetaResponse> AppService::Meta(
    const AppMetaRequest& request) const {
  std::string endpoint = endpoints::AppMeta(request.GetUser());
  auto res = http_client_.Get(endpoint);
  auto response = BuildResult<AppMetaResponse>(res);
  if (response.ok) {
    try {
      nlohmann::json j = nlohmann::json::parse(response.raw_body);
      response.data.SetMeta(j.dump());
    } catch (const std::exception& e) {
      response.ok = false;
      response.error.message =
          "Failed to deserialize AppMetaResponse: " + std::string(e.what());
    }
  }
  return response;
}
ApiResult<AppParamResponse> AppService::Parameters(
    const AppParamRequest& request) const {

  auto res = http_client_.Get(endpoints::kParameters);
  auto response = BuildResult<AppParamResponse>(res);
  if (response.ok) {
    try {
      nlohmann::json j = nlohmann::json::parse(response.raw_body);
      response.data.SetParamsJson(j.dump());
    } catch (const std::exception& e) {
      response.ok = false;
      response.error.message =
          "Failed to deserialize AppParamResponse: " + std::string(e.what());
    }
  }
  return response;
}

ApiResult<AppInfoResponse> AppService::Info(
    const AppInfoRequest& request) const {
  auto res = http_client_.Get(endpoints::kInfo);
  auto response = BuildResult<AppInfoResponse>(res);
  if (response.ok) {
    try {
      nlohmann::json j = nlohmann::json::parse(response.raw_body);
      response.data.SetName(j.value("name", ""));
      response.data.SetDescription(j.value("description", ""));
      response.data.SetTags(j.value("tags", std::vector<std::string>()));
    } catch (const std::exception& e) {
      response.ok = false;
      response.error.message =
          "Failed to deserialize AppInfoResponse: " + std::string(e.what());
    }
  }
  return response;
}

}  // namespace amssdk