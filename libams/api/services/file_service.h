#ifndef AMSSDK_FILE_SERVICE_H
#define AMSSDK_FILE_SERVICE_H

#include <experimental/filesystem>
#include "include/common/api_result.h"

namespace amssdk {

class ApiManager;
class HttpClient;
class Authorization;
class FileResponse;

class FileService {
 public:
  LIBAMS_EXPORT
  explicit FileService(ApiManager& api_manager);

  LIBAMS_EXPORT
  ApiResult<FileResponse> Upload(
      const std::experimental::filesystem::path& file,
      const std::string& user) const;

 private:
  HttpClient& http_client_;
  Authorization& auth_;
};

}  // namespace amssdk

#endif  // AMSSDK_FILE_SERVICE_H
