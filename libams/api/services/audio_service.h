#ifndef AMSSDK_AUDIO_SERVICE_H
#define AMSSDK_AUDIO_SERVICE_H
#include "include/common/api_result.h"

namespace amssdk {

class HttpClient;
class Authorization;
class ApiManager;
class TextToAudioRequest;
class TextToAudioResponse;
class AudioToTextRequest;
class AudioToTextResponse;

class AudioService {
 public:
  AudioService(ApiManager& api_manager);
  ApiResult<TextToAudioResponse> TextToAudio(
      const TextToAudioRequest& request) const;
  ApiResult<AudioToTextResponse> AudioToText(
      const AudioToTextRequest& request) const;

 private:
  HttpClient& http_client_;
  Authorization& auth_;
};
}  // namespace amssdk

#endif  //AMSSDK_AUDIO_SERVICE_H
