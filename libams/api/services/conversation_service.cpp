#include "conversation_service.h"

#include "api/api_manager.h"
#include "include/common/common.h"
#include "include/common/endpoints.h"
#include "include/conversation/conversation_request.h"
#include "include/conversation/conversation_response.h"
#include "serializer/deserializer.h"
#include "serializer/serializer_utils.h"

namespace amssdk {
ConversationService::ConversationService(ApiManager& api_manager)
    : http_client_(api_manager.GetHttpClient()),
      auth_(api_manager.GetAuthorization()) {}

ApiResult<SuggestedResponse> ConversationService::GetSuggested(
    const SuggestedRequest& request) const {
  std::string endpoint =
      endpoints::Suggested(request.GetMessageId(), request.GetUser());
  auto res = http_client_.Get(endpoint);

  auto response = BuildResult<SuggestedResponse>(res);
  if (response.ok) {
    try {
      nlohmann::json j = nlohmann::json::parse(response.raw_body);
      response.data = DeserializeSuggestedResponse(j);
    } catch (const std::exception& e) {
      response.ok = false;
      response.error.message =
          "Failed to deserialize SuggestedResponse: " + std::string(e.what());
    }
  }
  return response;
}

ApiResult<MessagesResponse> ConversationService::GetMessages(
    const MessagesRequest& request) const {
  nlohmann::json json_request;

  std::map<std::string, std::string> query;
  query["conversation_id"] = request.GetConversationId();
  query["user"] = request.GetUser();
  query["first_id"] = request.GetFirstId();
  query["limit"] = std::to_string(request.GetLimit());

  std::string endpoint = endpoints::Messages(query);
  auto res = http_client_.Get(endpoint);
  auto response = BuildResult<MessagesResponse>(res);
  if (response.ok) {
    try {
      nlohmann::json j = nlohmann::json::parse(response.raw_body);
      response.data = DeserializeMessagesResponse(j);
    } catch (const std::exception& e) {
      response.ok = false;
      response.error.message =
          "Failed to deserialize MessagesResponse: " + std::string(e.what());
    }
  }
  return response;
}

ApiResult<ConversationResponse> ConversationService::GetConversation(
    const ConversationRequest& request) const {
  nlohmann::json json_request;
  std::map<std::string, std::string> query;
  query["user"] = request.GetUser();
  query["last_id"] = request.GetLastId();
  query["limit"] = request.GetLimit();
  query["sort_rule"] = SerializerUtils::SortRuleToString(request.GetSortRule());
  std::string endpoint = endpoints::Conversations(query);
  auto res = http_client_.Get(endpoint);
  auto response = BuildResult<ConversationResponse>(res);
  if (response.ok) {
    try {
      nlohmann::json j = nlohmann::json::parse(response.raw_body);
      response.data = DeserializeConversationResponse(j);
    } catch (const std::exception& e) {
      response.ok = false;
      response.error.message = "Failed to deserialize ConversationResponse: " +
                               std::string(e.what());
    }
  }
  return response;
}
ApiResult<SimpleResponse> ConversationService::DeleteConversation(
    const DeleteConversationRequest& request) const {
  nlohmann::json json_request;
  json_request["user"] = request.GetUser();
  std::string endpoint =
      endpoints::DeleteConversation(request.GetConversationId());
  auto res = http_client_.Delete(json_request.dump(), endpoint);
  auto response = BuildResult<SimpleResponse>(res);
  response.data.result = response.success
                             ? SimpleResponse::ResultType::kSuccess
                             : SimpleResponse::ResultType::kFailure;
  return response;
}
ApiResult<RenameConversationResponse> ConversationService::RenameConversation(
    const RenameConversationRequest& request) const {
  nlohmann::json json_request;
  json_request["name"] = request.GetName();
  json_request["user"] = request.GetUser();
  json_request["auto_generate_name"] = request.GetAutoGenerateName();
  std::string endpoint =
      endpoints::RenameConversation(request.GetConversationId());
  auto res = http_client_.Post(json_request.dump(), endpoint);
  auto response = BuildResult<RenameConversationResponse>(res);
  if (response.ok) {
    try {
      nlohmann::json j = nlohmann::json::parse(response.raw_body);
      response.data = DeserializeRenameConversationResponse(j);
    } catch (const std::exception& e) {
      response.ok = false;
      response.error.message =
          "Failed to deserialize RenameConversationResponse: " +
          std::string(e.what());
    }
  }
  return response;
}
}  // namespace amssdk