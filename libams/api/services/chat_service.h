#ifndef AMSSDK_CHAT_SERVICE_H
#define AMSSDK_CHAT_SERVICE_H

#include <functional>
#include <memory>

#include "include/common/api_result.h"

namespace amssdk {

class ChatRequest;
class CompletionMessageRequest;
class ApiManager;
class HttpClient;
class Authorization;
class StreamEvent;

class ChatService {
 public:
  using StreamEventCallback = std::function<void(std::unique_ptr<StreamEvent>)>;

  explicit ChatService(ApiManager& api_manager);

  ApiResult<void> SendChatMessage(
      const ChatRequest& request,
      const StreamEventCallback& stream_event_callback = nullptr) const;
  ApiResult<void> SendCompletionMessage(
      const CompletionMessageRequest& request,
      const StreamEventCallback& stream_event_callback = nullptr) const;

 private:
  HttpClient& http_client_;
  Authorization& auth_;
};

}  // namespace amssdk

#endif  // AMSSDK_CHAT_SERVICE_H
