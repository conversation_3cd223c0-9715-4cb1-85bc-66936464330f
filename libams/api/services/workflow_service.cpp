#include "workflow_service.h"

#include "api/api_manager.h"
#include "include/common/endpoints.h"
#include "include/workflow.h"
#include "serializer/deserializer.h"
#include "serializer/serializer.h"
#include "serializer/stream_event_deserializer.h"

namespace amssdk {
WorkflowService::WorkflowService(ApiManager& apiManager)
    : http_client_(apiManager.GetHttpClient()),
      auth_(apiManager.GetAuthorization()) {}

ApiResult<WorkflowRunResponse> WorkflowService::WorkflowRun(
    const WorkflowRunRequest& request,
    const StreamEventCallback& stream_event_callback) const {

  if (request.GetResponseMode() == ResponseMode::BLOCKING) {}

  auto frame_buffer = std::make_shared<StreamFrameBuffer>();
  auto callback = [stream_event_callback, frame_buffer](const std::string& data,
                                                        intptr_t userdata) {
    (void)userdata;
    StreamEventDeserializer deserializer;

    for (auto& frame : frame_buffer->feed(data)) {
      auto event_type = deserializer.DeserializeStreamEvent(frame);
      if (event_type && stream_event_callback) {
        stream_event_callback(std::move(event_type));
      }
    }
    return true;
  };

  auto json_str = SerializeWorkflowRunRequest(request);
  auto res = http_client_.Post(json_str, endpoints::kWorkflowsRun, callback);
  auto response = BuildResult<WorkflowRunResponse>(res);

  nlohmann::json j = nlohmann::json::parse(frame_buffer->buf);

  response.data = DeserializeWorkflowRunResponse(j);

  return response;
}
ApiResult<WorkflowRunInfoResponse> WorkflowService::WorkflowRunInfo(
    const WorkflowRunInfoRequest& request) const {
  std::string endpoint = endpoints::WorkflowRunInfo(request.GetWorkflowRunId());
  auto res = http_client_.Get(endpoint);
  auto response = BuildResult<WorkflowRunInfoResponse>(res);
  if (response.ok) {
    try {
      nlohmann::json j = nlohmann::json::parse(response.raw_body);
      response.data = DeserializeWorkflowRunInfoResponse(j);
    } catch (const std::exception& e) {
      response.ok = false;
      response.error.message =
          "Failed to deserialize WorkflowRunInfoResponse: " +
          std::string(e.what());
    }
  }
  return response;
}
ApiResult<SimpleResponse> WorkflowService::WorkflowTaskStop(
    const WorkflowTaskStopRequest& request) const {
  auto endpoint = endpoints::WorkflowTaskStop(request.GetTaskId());
  auto res =
      http_client_.Post(SerializeWorkflowTaskStopRequest(request), endpoint);
  auto response = BuildResult<SimpleResponse>(res);
  response.data.result = response.success
                             ? SimpleResponse::ResultType::kSuccess
                             : SimpleResponse::ResultType::kFailure;
  return response;
}
ApiResult<WorkflowLogsResponse> WorkflowService::WorkflowLogs(
    const WorkflowLogsRequest& request) const {
  auto res = http_client_.Get(
      endpoints::WorkflowLogs({{"key_word", request.GetKeyWord()},
                               {"status", request.GetStatus()},
                               {"page", std::to_string(request.GetPage())},
                               {"limit", std::to_string(request.GetLimit())}}));

  auto response = BuildResult<WorkflowLogsResponse>(res);
  if (response.ok) {
    try {
      nlohmann::json j = nlohmann::json::parse(response.raw_body);
      response.data = DeserializeWorkflowLogsResponse(j);
    } catch (const std::exception& e) {
      response.ok = false;
      response.error.message = "Failed to deserialize WorkflowLogsResponse: " +
                               std::string(e.what());
    }
  }
  return response;
}
}  // namespace amssdk