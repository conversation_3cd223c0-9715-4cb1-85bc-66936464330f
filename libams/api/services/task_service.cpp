#include "task_service.h"

#include "api/api_manager.h"
#include "include/common/common.h"
#include "include/common/endpoints.h"
#include "include/task.h"
#include "serializer/serializer.h"
namespace amssdk {

TaskService::TaskService(ApiManager& api_manager)
    : http_client_(api_manager.GetHttpClient()),
      auth_(api_manager.GetAuthorization()) {}

ApiResult<SimpleResponse> TaskService::StopTask(
    const TaskStopRequest& request) const {
  const nlohmann::json json_data = SerializeTaskStopRequest(request);
  std::string endpoint = endpoints::TaskStop(request.GetTaskId());

  auto res = http_client_.Post(json_data, endpoint);

  ApiResult<SimpleResponse> response = BuildResult<SimpleResponse>(res);
  response.data.result = response.success
                             ? SimpleResponse::ResultType::kSuccess
                             : SimpleResponse::ResultType::kFailure;

  return response;
}

ApiResult<SimpleResponse> TaskService::SendFeedback(
    const FeedbackRequest& request) const {
  const nlohmann::json json_data = SerializeFeedbackRequest(request);
  std::string endpoint = endpoints::TaskFeedback(request.GetMessageId());
  auto res = http_client_.Post(json_data, endpoint);
  auto response = BuildResult<SimpleResponse>(res);
  response.data.result = response.success
                             ? SimpleResponse::ResultType::kSuccess
                             : SimpleResponse::ResultType::kFailure;
  return response;
}

}  // namespace amssdk
