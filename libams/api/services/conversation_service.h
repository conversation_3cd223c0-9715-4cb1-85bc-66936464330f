#ifndef AMSSDK_CONVERSATION_SERVICE_H
#define AMSSDK_CONVERSATION_SERVICE_H
#include "include/common/api_result.h"

namespace amssdk {

class MessagesRequest;
class MessagesResponse;
class SuggestedRequest;
class SuggestedResponse;
class Authorization;
class HttpClient;
class ApiManager;
class ConversationRequest;
class ConversationResponse;
class DeleteConversationRequest;
class DeleteConversationResponse;
class RenameConversationRequest;
class RenameConversationResponse;
class SimpleResponse;

class ConversationService {

 public:
  explicit ConversationService(ApiManager& api_manager);

  ApiResult<SuggestedResponse> GetSuggested(
      const SuggestedRequest& request) const;
  ApiResult<MessagesResponse> GetMessages(const MessagesRequest& request) const;
  ApiResult<ConversationResponse> GetConversation(
      const ConversationRequest& request) const;
  ApiResult<SimpleResponse> DeleteConversation(
      const DeleteConversationRequest& request) const;
  ApiResult<RenameConversationResponse> RenameConversation(
      const RenameConversationRequest& request) const;

 private:
  HttpClient& http_client_;
  Authorization& auth_;
};
}  // namespace amssdk

#endif  //AMSSDK_CONVERSATION_SERVICE_H
