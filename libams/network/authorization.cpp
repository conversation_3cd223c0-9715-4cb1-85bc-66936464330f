#include "authorization.h"

amssdk::Authorization::~Authorization() {
  netimpl::components::EncodedAuthentication().SecureStringClear(this->key_);
}

bool amssdk::Authorization::SetKey(const std::string& key) noexcept {
  if (!key.empty()) {
    this->key_ = key;
    if (this->openai_auth_headers_.count("Authorization") > 0) {
      this->openai_auth_headers_.erase("Authorization");
    }
    this->openai_auth_headers_["Authorization"] = ("Bearer " + this->key_);
    return true;
  }
  return false;
}

bool amssdk::Authorization::SetAzureKey(const std::string& key) noexcept {
  if (!key.empty()) {
    this->key_ = key;
    if (this->azure_auth_headers_.size() > 0) {
      this->azure_auth_headers_.clear();
    }
    this->azure_auth_headers_["api-key"] = this->key_;
    return true;
  }
  return false;
}

bool amssdk::Authorization::SetAzureKeyAD(const std::string& key) noexcept {
  if (!key.empty()) {
    this->key_ = key;
    if (this->azure_auth_headers_.size() > 0) {
      this->azure_auth_headers_.clear();
    }
    this->azure_auth_headers_["Authorization"] = ("Bearer " + this->key_);
    return true;
  }
  return false;
}

bool amssdk::Authorization::SetKeyFile(const fs::path& path) noexcept {
  if (fs::exists(path) && fs::is_regular_file(path) &&
      fs::file_size(path) > 0) {
    std::ifstream file(path);
    if (file.is_open()) {
      std::getline(file, this->key_);
      if (this->openai_auth_headers_.count("Authorization") > 0) {
        this->openai_auth_headers_.erase("Authorization");
      }
      this->openai_auth_headers_["Authorization"] = ("Bearer " + this->key_);
      return true;
    }
  }
  return false;
}

bool amssdk::Authorization::SetAzureKeyFile(const fs::path& path) noexcept {
  if (fs::exists(path) && fs::is_regular_file(path) &&
      fs::file_size(path) > 0) {
    std::ifstream file(path);
    if (file.is_open()) {
      std::getline(file, this->key_);
      if (this->azure_auth_headers_.size() > 0) {
        this->azure_auth_headers_.clear();
      }
      this->azure_auth_headers_["api-key"] = this->key_;
      return true;
    }
  }
  return false;
}

bool amssdk::Authorization::SetAzureKeyFileAD(const fs::path& path) noexcept {
  if (fs::exists(path) && fs::is_regular_file(path) &&
      fs::file_size(path) > 0) {
    std::ifstream file(path);
    if (file.is_open()) {
      std::getline(file, this->key_);
      if (this->azure_auth_headers_.size() > 0) {
        this->azure_auth_headers_.clear();
      }
      this->azure_auth_headers_["Authorization"] = ("Bearer " + this->key_);
      return true;
    }
  }
  return false;
}

bool amssdk::Authorization::SetKeyEnv(const std::string& var) noexcept {
  if (!var.empty()) {
    const char* key = std::getenv(var.data());
    if (key != nullptr) {
      this->key_ = key;
      if (this->openai_auth_headers_.count("Authorization") > 0) {
        this->openai_auth_headers_.erase("Authorization");
      }
      this->openai_auth_headers_["Authorization"] = ("Bearer " + this->key_);
      return true;
    }
    return false;
  }
  return false;
}

bool amssdk::Authorization::SetAzureKeyEnv(const std::string& var) noexcept {
  if (!var.empty()) {
    const char* key = std::getenv(var.data());
    if (key != nullptr) {
      this->key_ = key;
      if (this->azure_auth_headers_.size() > 0) {
        this->azure_auth_headers_.clear();
      }
      this->azure_auth_headers_["api-key"] = this->key_;
      return true;
    }
    return false;
  }
  return false;
}

bool amssdk::Authorization::SetAzureKeyEnvAD(const std::string& var) noexcept {
  if (!var.empty()) {
    const char* key = std::getenv(var.data());
    if (key != nullptr) {
      this->key_ = key;
      if (this->azure_auth_headers_.size() > 0) {
        this->azure_auth_headers_.clear();
      }
      this->azure_auth_headers_["Authorization"] = ("Bearer " + this->key_);
      return true;
    }
    return false;
  }
  return false;
}

bool amssdk::Authorization::SetOrganization(const std::string& org) noexcept {
  if (!org.empty()) {
    this->org_ = std::move(org);
    if (this->openai_auth_headers_.count("OpenAI-Organization") > 0) {
      this->openai_auth_headers_.erase("OpenAI-Organization");
    }
    this->openai_auth_headers_["OpenAI-Organization"] = this->org_;
    return true;
  }
  return false;
}

bool amssdk::Authorization::SetOrganizationFile(const fs::path& path) noexcept {
  if (fs::exists(path) && fs::is_regular_file(path) &&
      fs::file_size(path) > 0) {
    std::ifstream file(path);
    if (file.is_open()) {
      std::getline(file, this->key_);
      if (this->openai_auth_headers_.count("OpenAI-Organization") > 0) {
        this->openai_auth_headers_.erase("OpenAI-Organization");
      }
      this->openai_auth_headers_["OpenAI-Organization"] = this->org_;
      return true;
    }
  }
  return false;
}

bool amssdk::Authorization::SetOrganizationEnv(
    const std::string& var) noexcept {
  if (!var.empty()) {
    const char* org = std::getenv(var.data());
    if (org != nullptr) {
      this->org_ = org;
      if (this->openai_auth_headers_.count("OpenAI-Organization") > 0) {
        this->openai_auth_headers_.erase("OpenAI-Organization");
      }
      this->openai_auth_headers_["OpenAI-Organization"] = this->org_;
      return true;
    }
    return false;
  }
  return false;
}

void amssdk::Authorization::SetProxies(
    const std::initializer_list<std::pair<const std::string, std::string>>&
        hosts) noexcept {
  this->proxies_ = netimpl::components::Proxies(hosts);
}

void amssdk::Authorization::SetProxies(
    std::initializer_list<std::pair<const std::string, std::string>>&&
        hosts) noexcept {
  this->proxies_ = netimpl::components::Proxies(std::move(hosts));
}

void amssdk::Authorization::SetProxies(
    const std::map<std::string, std::string>& hosts) noexcept {
  this->proxies_ = netimpl::components::Proxies(hosts);
}

void amssdk::Authorization::SetProxies(
    std::map<std::string, std::string>&& hosts) noexcept {
  this->proxies_ = netimpl::components::Proxies(std::move(hosts));
}

void amssdk::Authorization::SetProxyAuth(
    const std::map<std::string, netimpl::components::EncodedAuthentication>&
        proto_up) noexcept {
  this->proxyAuth_ = netimpl::components::ProxyAuthentication(proto_up);
}