#include "http_client.h"
namespace amssdk {
Response HttpClient::Post(const std::string& json_str,
                          const std::string& endpoint,
                          const StreamCallback& stream_callback) const {
  Response res;

  auto write_callback = netimpl::components::WriteCallback(
      [&](std::string data, intptr_t userdata) {
        try {
          auto json_data = nlohmann::json::parse(data);
          if (json_data.contains("status") && json_data.contains("message") &&
              json_data.contains("code")) {
            res.status_code = json_data["status"].get<int>();
            res.reason = json_data["message"].get<std::string>();
            res.raw_json = json_data;
          }
        } catch (const std::exception& e) {}

        return stream_callback(data, userdata);
      });

  try {
    res = this->Request(
        Method::HTTP_POST, this->base_url_, endpoint, "application/json",
        this->auth_.GetAuthorizationHeaders(),
        netimpl::components::Body(json_str),
        stream_callback ? write_callback : netimpl::components::WriteCallback{},
        this->auth_.GetProxies(), this->auth_.GetProxyAuth(),
        this->auth_.GetMaxTimeout());
  } catch (const std::exception& e) {}

  return res;
}

FutureResponse HttpClient::PostAsync(
    const std::string& json_str, const std::string& endpoint,
    const StreamCallback& stream_callback) const {
  return std::async(std::launch::async, &HttpClient::Post, this, json_str,
                    endpoint, stream_callback);
}

Response HttpClient::Get(const std::string& endpoint) const {
  Response res;
  try {
    res = this->Request(
        Method::HTTP_GET, this->base_url_, endpoint, "application/json",
        this->auth_.GetAuthorizationHeaders(),
        netimpl::components::WriteCallback{}, this->auth_.GetProxies(),
        this->auth_.GetProxyAuth(), this->auth_.GetMaxTimeout());
  } catch (const exception::OpenAIException& e) {
    res.reason = e.what();
    res.status_code = e.status_code();
    res.raw_json = nlohmann::json::parse(e.raw_json());
  }

  return res;
}

FutureResponse HttpClient::GetAsync(const std::string& endpoint) {
  return std::async(std::launch::async, &HttpClient::Get, this, endpoint);
}

Response HttpClient::Delete(const std::string& json_str,
                            const std::string& endpoint) const {
  Response res;
  try {
    res = this->Request(
        Method::HTTP_DELETE, this->base_url_, endpoint, "application/json",
        this->auth_.GetAuthorizationHeaders(),
        netimpl::components::Body(json_str),
        netimpl::components::WriteCallback{}, this->auth_.GetProxies(),
        this->auth_.GetProxyAuth(), this->auth_.GetMaxTimeout());
  } catch (const exception::OpenAIException& e) {
    res.reason = e.what();
    res.status_code = e.status_code();
  }
  return res;
}

FutureResponse HttpClient::DeleteAsync(const std::string& json_str,
                                       const std::string& endpoint) const {
  return std::async(std::launch::async, &HttpClient::Delete, this, json_str,
                    endpoint);
}

Response HttpClient::Download() {
  throw exception::OpenAIException("Not implemented",
                                   exception::EType::E_BADREQUEST,
                                   "HttpClient::Download()");
}

FutureResponse HttpClient::DownloadAsync() {
  return std::async(std::launch::async, &HttpClient::Download, this);
}

Response HttpClient::MultipartForm(
    const std::string& endpoint,
    std::map<std::string, std::string> form) const {

  netimpl::components::Multipart form_data;
  for (const auto& form_item : form) {
    if (form_item.first == "file") {
      form_data.parts.emplace_back(form_item.first,
                                   netimpl::components::File{form_item.second});
      continue;
    }
    form_data.parts.emplace_back(form_item.first, form_item.second);
  }

  Response res;
  try {
    res = this->Request(
        Method::HTTP_POST, this->base_url_, endpoint, "multipart/form-data",
        this->auth_.GetAuthorizationHeaders(), std::move(form_data),
        this->auth_.GetProxies(), this->auth_.GetProxyAuth(),
        this->auth_.GetMaxTimeout());
  } catch (const exception::OpenAIException& e) {
    res.reason = e.what();
    res.status_code = e.status_code();
  }
  return res;
}

FutureResponse HttpClient::UploadAsync(
    const std::string& endpoint,
    const std::map<std::string, std::string>& form_data) const {
  return std::async(std::launch::async, &HttpClient::MultipartForm, this,
                    endpoint, form_data);
}
}  // namespace amssdk
