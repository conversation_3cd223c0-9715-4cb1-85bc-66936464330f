#ifndef AMSSDK_HTTP_CLIENT_H
#define AMSSDK_HTTP_CLIENT_H

#include "authorization.h"
#include "network.h"

namespace amssdk {

class HttpClient final : Network {

 public:
  using StreamCallback = std::function<bool(std::string, intptr_t)>;
  explicit HttpClient(const std::string& root = "")
      : Network(root), base_url_(root) {}

  Response Post(const std::string& json_str, const std::string& endpoint,
                const StreamCallback& stream_callback = nullptr) const;
  Response Get(const std::string& endpoint) const;
  Response Delete(const std::string& json_str,
                  const std::string& endpoint) const;
  Response MultipartForm(const std::string& endpoint,
                         std::map<std::string, std::string> form) const;
  Response Download();

  FutureResponse PostAsync(
      const std::string& json_str, const std::string& endpoint,
      const StreamCallback& stream_callback = nullptr) const;
  FutureResponse GetAsync(const std::string& endpoint);
  FutureResponse DeleteAsync(const std::string& json_str,
                             const std::string& endpoint) const;
  FutureResponse UploadAsync(
      const std::string& endpoint,
      const std::map<std::string, std::string>& form_data) const;
  FutureResponse DownloadAsync();

  //getter and setter
  inline void SetBaseUrl(const std::string& url) { this->base_url_ = url; }

 private:
  Authorization& auth_ = Authorization::Authorizer();
  std::string base_url_;
};

}  // namespace amssdk

#endif  //AMSSDK_HTTP_CLIENT_H
