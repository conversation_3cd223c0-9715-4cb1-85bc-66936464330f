#ifndef AMSSDK_AUTHORIZATION_H
#define AMSSDK_AUTHORIZATION_H

#include <experimental/filesystem>

// Filesystem compatibility layer for C++14/17 transition
namespace amssdk {
namespace fs = std::experimental::filesystem;
}

#include "network.h"

namespace amssdk {
class Authorization final {
 public:  // cons/des, operator deletions
  Authorization() = default;
  NON_COPYABLE(Authorization)
  NON_MOVABLE(Authorization)
  ~Authorization();

 public:  // member methods
  /**
   * @brief Singleton paradigm access method.
   * @return A reference to the singleton instance of this class
   *         to be used in all component classes.
   */
  static Authorization& Authorizer() noexcept {
    static Authorization instance;
    return instance;
  }

  /**
   * @brief Sets the authorization key for the OpenAI API as the passed string.
   * @param key The authorization key to use in component calls.
   * @returns True if the key was set successfully, false otherwise.
   */
  LIBAMS_EXPORT bool SetKey(const std::string& key) noexcept;

  /**
   * @brief Sets the authorization key for the Azure OpenAI API as the passed string.
   * @param key The authorization key to use in Azure component calls.
   * @returns True if the key was set successfully, false otherwise.
   */
  LIBAMS_EXPORT bool SetAzureKey(const std::string& key) noexcept;

  /**
   * @brief Sets the Active Directory authorization token for the Azure OpenAI API as the passed string.
   * @param key The authorization key to use in Azure component calls.
   * @returns True if the key was set successfully, false otherwise.
   */
  LIBAMS_EXPORT bool SetAzureKeyAD(const std::string& key) noexcept;

  /**
   * @brief Sets the authorization key for the OpenAI API as the first line present in the file at the passed path.
   * @param path The path to the file containing the authorization key.
   * @returns True if the key was set successfully, false otherwise.
   */
  LIBAMS_EXPORT bool SetKeyFile(const fs::path& path) noexcept;

  /**
   * @brief Sets the authorization key for the Azure OpenAI API as the first line present in the file at the passed path.
   * @param path The path to the file containing the authorization key.
   * @returns True if the key was set successfully, false otherwise.
   */
  LIBAMS_EXPORT bool SetAzureKeyFile(const fs::path& path) noexcept;

  /**
   * @brief Sets the Active Directory authorization token for the Azure OpenAI API as the first line present in the file at the passed path.
   * @param path The path to the file containing the authorization key.
   * @returns True if the key was set successfully, false otherwise.
   */
  LIBAMS_EXPORT bool SetAzureKeyFileAD(const fs::path& path) noexcept;

  /**
   * @brief Sets the authorization key for the OpenAI API as the value stored in the environment variable with the passed name.
   * @param var The name of the environment variable to retrieve the authorization key from.
   * @returns True if the key was set successfully, false otherwise.
   */
  LIBAMS_EXPORT bool SetKeyEnv(const std::string& var) noexcept;

  /**
   * @brief Sets the authorization key for the Azure OpenAI API as the value stored in the environment variable with the passed name.
   * @param var The name of the environment variable to retrieve the authorization key from.
   * @returns True if the key was set successfully, false otherwise.
   */
  LIBAMS_EXPORT bool SetAzureKeyEnv(const std::string& var) noexcept;

  /**
   * @brief Sets the Active Directory authorization token for the Azure OpenAI API as the value stored in the environment variable with the passed name.
   * @param var The name of the environment variable to retrieve the authorization key from.
   * @returns True if the key was set successfully, false otherwise.
   */
  LIBAMS_EXPORT bool SetAzureKeyEnvAD(const std::string& var) noexcept;

  /**
   * @brief Sets the organization identifier as the passed string for use in component calls.
   * @param org The organization identifier to use in component calls.
   * @returns True if the ID was set successfully, false otherwise.
   */
  LIBAMS_EXPORT bool SetOrganization(const std::string& org) noexcept;

  /**
   * @brief Sets the organization identifier as the first line present in the file at the passed path for use in component calls.
   * @param path The path to the file containing the organization identifier.
   * @returns True if the ID was set successfully, false otherwise.
   */
  LIBAMS_EXPORT bool SetOrganizationFile(const fs::path& path) noexcept;

  /**
   * @brief Sets the organization identifier as the value stored in the environment variable with the passed name for use in component calls.
   * @param var The name of the environment variable to retrieve the organization identifier from.
   * @returns True if the ID was set successfully, false otherwise.
   */
  LIBAMS_EXPORT bool SetOrganizationEnv(const std::string& var) noexcept;

  /**
   * @brief Sets proxies to use for component calls.
   * @param hosts The hosts to use as proxies in paired { "protocol", "host" } format.
   */
  LIBAMS_EXPORT void SetProxies(
      const std::initializer_list<std::pair<const std::string, std::string>>&
          hosts) noexcept;

  /**
   * @brief Sets proxies to use for component calls.
   * @param hosts The hosts to use as proxies in paired { "protocol", "host" } format.
   */
  LIBAMS_EXPORT void SetProxies(
      std::initializer_list<std::pair<const std::string, std::string>>&&
          hosts) noexcept;

  /**
   * @brief Sets proxies to use for component calls.
   * @param hosts The hosts to use as proxies in paired { "protocol", "host" } format.
   */
  LIBAMS_EXPORT void SetProxies(
      const std::map<std::string, std::string>& hosts) noexcept;

  /**
   * @brief Sets proxies to use for component calls.
   * @param hosts The hosts to use as proxies in paired { "protocol", "host" } format.
   */
  LIBAMS_EXPORT void SetProxies(
      std::map<std::string, std::string>&& hosts) noexcept;

  /**
   * @brief Sets authentication information for proxies per-protocol.
   * @param proto_up A {protocol, {uname, passwd}} map to use for authentication with proxies on a per-protocol basis.
   */
  LIBAMS_EXPORT void SetProxyAuth(
      const std::map<std::string, netimpl::components::EncodedAuthentication>&
          proto_up) noexcept;

  /**
   * @brief Sets the timeout for component calls in milliseconds.
   * @param ms The timeout value in milliseconds.
   */
  LIBAMS_EXPORT void SetMaxTimeout(int32_t ms) noexcept {
    this->timeout_ = netimpl::components::Timeout(ms);
  }

  /**
   * @brief Returns currently the set authorization key.
   * @return The currently set authorization key.
   */
  constexpr const std::string& GetKey() const noexcept { return this->key_; }

  /**
   * @brief Returns the currently set organization identifier.
   * @return The currently set organization identifier.
   */
  constexpr const std::string& GetOrganization() const noexcept {
    return this->org_;
  }

  /**
   * @brief Returns the currently set proxies.
   * @return The currently set proxies.
   */
  netimpl::components::Proxies GetProxies() const noexcept {
    return this->proxies_;
  }

  /**
   * @brief Returns the currently set proxy authentication information.
   * @return The currently set proxy authentication information.
   */
  netimpl::components::ProxyAuthentication GetProxyAuth() const noexcept {
    return this->proxyAuth_;
  }

  /**
   * @brief Returns the currently set timeout.
   * @return The currently set timeout.
   */
  netimpl::components::Timeout GetMaxTimeout() const noexcept {
    return this->timeout_;
  }

  /**
   * @brief Returns an authorization header with the currently set authorization information for use in component calls.
   * @return An authorization header with the currently set authorization information.
   */
  constexpr const netimpl::components::Header& GetAuthorizationHeaders()
      const noexcept {
    return this->openai_auth_headers_;
  }

  /**
   * @brief Returns an authorization header with the currently set Azure authorization information for use in Azure component calls.
   * @return An authorization header with the currently set Azure authorization information.
   */
  constexpr const netimpl::components::Header& GetAzureAuthorizationHeaders()
      const noexcept {
    return this->azure_auth_headers_;
  }

 private:  // member variables
  std::string key_, org_;
  netimpl::components::Header openai_auth_headers_, azure_auth_headers_;
  netimpl::components::Proxies proxies_;
  netimpl::components::ProxyAuthentication proxyAuth_;
  netimpl::components::Timeout timeout_ = {30000};
};
}  // namespace amssdk
#endif
