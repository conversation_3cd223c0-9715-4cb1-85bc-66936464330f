#ifndef AMSSDK_AUDIO_H
#define AMSSDK_AUDIO_H

#include <experimental/filesystem>
#include <string>

namespace amssdk {
class AudioToTextRequest {
 public:
  AudioToTextRequest& SetFilePath(
      const std::experimental::filesystem::path& file_path) {
    file_path_ = file_path;
    return *this;
  }
  AudioToTextRequest& SetUser(const std::string& user) {
    user_ = user;
    return *this;
  }
  const std::experimental::filesystem::path& GetFilePath() const noexcept {
    return file_path_;
  }
  const std::string& GetUser() const noexcept { return user_; }

 private:
  std::experimental::filesystem::path file_path_;
  std::string user_;
};

class AudioToTextResponse {
 public:
  AudioToTextResponse& SetText(const std::string& text) {
    text_ = text;
    return *this;
  }
  const std::string& GetText() const noexcept { return text_; }

 private:
  std::string text_;
};

class TextToAudioRequest {
 public:
  TextToAudioRequest& SetText(const std::string& text) {
    text_ = text;
    return *this;
  }
  TextToAudioRequest& SetUser(const std::string& user) {
    user_ = user;
    return *this;
  }
  TextToAudioRequest& SetMessageId(const std::string& message_id) {
    message_id_ = message_id;
    return *this;
  }

  const std::string& GetText() const noexcept { return text_; }
  const std::string& GetUser() const noexcept { return user_; }
  const std::string& GetMessageId() const noexcept { return message_id_; }

 private:
  std::string text_;
  std::string message_id_;
  std::string user_;
};

class TextToAudioResponse {
 public:
  TextToAudioResponse& SetBytes(const std::string& bytes) {
    bytes_ = bytes;
    return *this;
  }
  const std::string& GetBytes() const noexcept { return bytes_; }

 private:
  std::string bytes_;
};

}  // namespace amssdk

#endif  //AMSSDK_AUDIO_H
