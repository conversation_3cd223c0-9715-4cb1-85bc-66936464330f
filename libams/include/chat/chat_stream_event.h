#ifndef AMSSDK_CHAT_RESPONSE_H
#define AMSSDK_CHAT_RESPONSE_H

#include <chrono>
#include <string>
#include <vector>

#include "include/common/common.h"

namespace amssdk {

class StreamEvent {
 public:
  enum class Type {
    MESSAGE,
    AGENT_MESSAGE,
    MESSAGE_REPLACE,
    AGENT_THOUGHT,
    MESSAGE_FILE,
    MESSAGE_END,
    TTS_MESSAGE,
    TTS_MESSAGE_END,
    WORKFLOW_STARTED,
    NODE_STARTED,
    NODE_FINISHED,
    WORKFLOW_FINISHED,
    ERR
  };

  explicit StreamEvent(const Type type) : type_(type) {}
  virtual ~StreamEvent() = default;

  Type type() const noexcept { return type_; }

  template <typename T>
  const T* as() const {
    return dynamic_cast<const T*>(this);
  }

 private:
  Type type_;
};

class MessageEvent : public StreamEvent {
 public:
  MessageEvent(std::string task_id, std::string message_id,
               std::string conversation_id, std::string answer)
      : StreamEvent(Type::MESSAGE),
        task_id_(std::move(task_id)),
        message_id_(std::move(message_id)),
        conversation_id_(std::move(conversation_id)),
        answer_(std::move(answer)) {}

  const std::string& TaskId() const noexcept { return task_id_; }
  const std::string& MessageId() const noexcept { return message_id_; }
  const std::string& ConversationId() const noexcept {
    return conversation_id_;
  }
  const std::string& Answer() const noexcept { return answer_; }

 private:
  std::string task_id_;
  std::string message_id_;
  std::string conversation_id_;
  std::string answer_;
  int created_at_;
};

class AgentThoughtEvent : public StreamEvent {
 public:
  AgentThoughtEvent(std::string id, std::string task_id, std::string message_id,
                    std::string conversation_id, std::string observation,
                    int position, std::string thought, std::string tool,
                    std::string tool_input,
                    std::vector<std::string> message_files)
      : StreamEvent(Type::AGENT_THOUGHT),
        id_(std::move(id)),
        task_id_(std::move(task_id)),
        message_id_(std::move(message_id)),
        conversation_id_(std::move(conversation_id)),
        observation_(std::move(observation)),
        position_(position),
        thought_(std::move(thought)),
        tool_(std::move(tool)),
        tool_input_(std::move(tool_input)),
        message_files_(std::move(message_files)) {}

  const std::string& Id() const noexcept { return id_; }
  const std::string& TaskId() const noexcept { return task_id_; }
  const std::string& MessageId() const noexcept { return message_id_; }
  const std::string& ConversationId() const noexcept {
    return conversation_id_;
  }
  const std::string& Observation() const noexcept { return observation_; }
  int Position() const noexcept { return position_; }
  const std::string& Thought() const noexcept { return thought_; }
  const std::string& Tool() const noexcept { return tool_; }
  const std::string& ToolInput() const noexcept { return tool_input_; }
  const std::vector<std::string>& MessageFiles() const noexcept {
    return message_files_;
  }

 private:
  std::string id_;
  std::string task_id_;
  std::string message_id_;
  std::string conversation_id_;
  std::string observation_;
  int position_;
  std::string thought_;
  std::string tool_;
  std::string tool_input_;
  std::vector<std::string> message_files_;
};

class MessageEndEvent : public StreamEvent {
 public:
  MessageEndEvent(std::string task_id, std::string message_id,
                  std::string conversation_id, MetaData metadata)
      : StreamEvent(Type::MESSAGE_END),
        task_id_(std::move(task_id)),
        message_id_(std::move(message_id)),
        conversation_id_(std::move(conversation_id)),
        metadata_(std::move(metadata)) {}

  const std::string& TaskId() const noexcept { return task_id_; }
  const std::string& MessageId() const noexcept { return message_id_; }
  const std::string& ConversationId() const noexcept {
    return conversation_id_;
  }
  const MetaData& Metadata() const noexcept { return metadata_; }

 private:
  std::string task_id_;
  std::string message_id_;
  std::string conversation_id_;
  MetaData metadata_;
};

class MessageFileEvent : public StreamEvent {
 public:
  MessageFileEvent(std::string id, std::string belongs_to,
                   std::string conversation_id,
                   FileAttachment::FileType file_type, std::string url)
      : StreamEvent(Type::MESSAGE_FILE),
        id_(std::move(id)),
        belongs_to_(std::move(belongs_to)),
        conversation_id_(std::move(conversation_id)),
        file_type_(file_type),
        url_(std::move(url)) {}

  std::string Id() const noexcept { return id_; }
  std::string BelongsTo() const noexcept { return belongs_to_; }
  std::string ConversationId() const noexcept { return conversation_id_; }
  FileAttachment::FileType FileType() const noexcept { return file_type_; }
  const std::string& Url() const noexcept { return url_; }

 private:
  std::string id_;
  std::string belongs_to_;
  std::string conversation_id_;
  FileAttachment::FileType file_type_;
  std::string url_;
};

class TtsMessageEvent : public StreamEvent {
 public:
  TtsMessageEvent(std::string task_id, std::string message_id,
                  std::string audio, int created_at)
      : StreamEvent(Type::TTS_MESSAGE),
        task_id_(std::move(task_id)),
        message_id_(std::move(message_id)),
        audio_(std::move(audio)),
        created_at_(created_at) {}

  const std::string& TaskId() const noexcept { return task_id_; }
  const std::string& MessageId() const noexcept { return message_id_; }
  const std::string& Audio() const noexcept { return audio_; }
  int CreatedAt() const noexcept { return created_at_; }

 private:
  std::string task_id_;
  std::string message_id_;
  std::string audio_;
  int created_at_;
};

class TtsMessageEndEvent : public StreamEvent {
 public:
  TtsMessageEndEvent(std::string task_id, std::string message_id,
                     std::string audio, int created_at)
      : StreamEvent(Type::TTS_MESSAGE_END),
        task_id_(std::move(task_id)),
        message_id_(std::move(message_id)),
        audio_(std::move(audio)),
        created_at_(created_at) {}
  std::string TaskId() const noexcept { return task_id_; }
  std::string MessageId() const noexcept { return message_id_; }
  std::string Audio() const noexcept { return audio_; }
  int CreatedAt() const noexcept { return created_at_; }

 private:
  std::string task_id_;
  std::string message_id_;
  std::string audio_;
  int created_at_;
};

class MessageReplaceEvent : public StreamEvent {
 public:
  MessageReplaceEvent(std::string task_id, std::string message_id,
                      std::string conversation_id, std::string answer,
                      int created_at)
      : StreamEvent(Type::MESSAGE_REPLACE),
        task_id_(std::move(task_id)),
        message_id_(std::move(message_id)),
        conversation_id_(std::move(conversation_id)),
        answer_(std::move(answer)),
        created_at_(created_at) {}

  std::string TaskId() const noexcept { return task_id_; }
  std::string MessageId() const noexcept { return message_id_; }
  std::string ConversationId() const noexcept { return conversation_id_; }
  std::string Answer() const noexcept { return answer_; }
  int CreatedAt() const noexcept { return created_at_; }

 private:
  std::string task_id_;
  std::string message_id_;
  std::string conversation_id_;
  std::string answer_;
  int created_at_;
};

class ErrorEvent : public StreamEvent {
 public:
  ErrorEvent(std::string task_id, std::string message_id, const int status,
             std::string code, std::string message)
      : StreamEvent(Type::ERR),
        status_(status),
        task_id_(std::move(task_id)),
        message_id_(std::move(message_id)),
        code_(std::move(code)),
        message_(std::move(message)) {}

  const std::string& TaskId() const noexcept { return task_id_; }
  const std::string& MessageId() const noexcept { return message_id_; }
  int Status() const noexcept { return status_; }
  const std::string& Code() const noexcept { return code_; }
  const std::string& Message() const noexcept { return message_; }

 private:
  int status_;
  std::string task_id_;
  std::string message_id_;
  std::string code_;
  std::string message_;
};

class WorkflowEvent final : public StreamEvent {
 public:
  WorkflowEvent(std::string task_id, std::string workflow_run_id,
                std::string data_json, Type type = Type::NODE_STARTED)
      : StreamEvent(type),
        task_id_(std::move(task_id)),
        workflow_run_id_(std::move(workflow_run_id)),
        data_json_(std::move(data_json)) {}

  std::string GetTaskId() const noexcept { return task_id_; }
  std::string GetWorkflowRunId() const noexcept { return workflow_run_id_; }
  std::string GetDataJson() const noexcept { return data_json_; }

 private:
  std::string task_id_;
  std::string workflow_run_id_;
  std::string data_json_;
};

}  // namespace amssdk

#endif  //AMSSDK_CHAT_RESPONSE_H
