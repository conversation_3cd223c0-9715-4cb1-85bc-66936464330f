#ifndef AMSSDK_WORKFLOW_H
#define AMSSDK_WORKFLOW_H
#include <map>
#include <string>
#include <vector>

#include "common/common.h"

namespace amssdk {

struct FileAttachment;

class WorkflowRunRequest {
 public:
  WorkflowRunRequest& SetUser(const std::string& user) {
    user_ = user;
    return *this;
  }
  WorkflowRunRequest& SetInputs(
      const std::map<std::string, std::string>& inputs) {
    inputs_ = inputs;
    return *this;
  }
  WorkflowRunRequest& SetResponseMode(ResponseMode mode) {
    response_mode_ = mode;
    return *this;
  }
  WorkflowRunRequest& AddFile(const FileAttachment& file) {
    files_.push_back(file);
    return *this;
  }

  std::map<std::string, std::string> GetInputs() const { return inputs_; }
  ResponseMode GetResponseMode() const { return response_mode_; }
  std::vector<FileAttachment> GetFiles() const { return files_; }
  std::string GetUser() const { return user_; }

 private:
  std::map<std::string, std::string> inputs_;
  ResponseMode response_mode_ = ResponseMode::STREAMING;
  std::string user_;
  std::vector<FileAttachment> files_;
};

class WorkflowRunResponse {
 public:
  WorkflowRunResponse& SetTaskId(const std::string& task_id) {
    task_id_ = task_id;
    return *this;
  }
  WorkflowRunResponse& SetWorkflowRunId(const std::string& workflow_run_id) {
    workflow_run_id_ = workflow_run_id;
    return *this;
  }
  WorkflowRunResponse& SetDataJson(const std::string& data_json) {
    data_json_ = data_json;
    return *this;
  }
  const std::string& GetTaskId() const noexcept { return task_id_; }
  const std::string& GetWorkflowRunId() const noexcept {
    return workflow_run_id_;
  }
  const std::string& GetDataJson() const noexcept { return data_json_; }

 private:
  std::string task_id_;
  std::string workflow_run_id_;
  std::string data_json_;
};

class WorkflowRunInfoRequest {
 public:
  WorkflowRunInfoRequest& SetWorkflowRunId(const std::string& workflow_run_id) {
    workflow_run_id_ = workflow_run_id;
    return *this;
  }
  const std::string& GetWorkflowRunId() const noexcept {
    return workflow_run_id_;
  }

 private:
  std::string workflow_run_id_;
};

class WorkflowRunInfoResponse {
 public:
  WorkflowRunInfoResponse& SetId(const std::string& id) {
    id_ = id;
    return *this;
  }
  WorkflowRunInfoResponse& SetWorkflowId(const std::string& workflow_id) {
    workflow_id_ = workflow_id;
    return *this;
  }
  WorkflowRunInfoResponse& SetStatus(const std::string& status) {
    status_ = status;
    return *this;
  }
  WorkflowRunInfoResponse& SetInputs(const nlohmann::json& inputs) {
    inputs_ = inputs;
    return *this;
  }
  WorkflowRunInfoResponse& SetOutputs(const nlohmann::json& outputs) {
    outputs_ = outputs;
    return *this;
  }
  WorkflowRunInfoResponse& SetError(const std::string& error) {
    error_ = error;
    return *this;
  }
  WorkflowRunInfoResponse& SetTotalSteps(int total_steps) {
    total_steps_ = total_steps;
    return *this;
  }
  WorkflowRunInfoResponse& SetTotalTokens(int total_tokens) {
    total_tokens_ = total_tokens;
    return *this;
  }
  WorkflowRunInfoResponse& SetCreatedAt(int created_at) {
    created_at_ = created_at;
    return *this;
  }
  WorkflowRunInfoResponse& SetFinishedAt(int finished_at) {
    finished_at_ = finished_at;
    return *this;
  }
  WorkflowRunInfoResponse& SetElapsedTime(int elapsed_time) {
    elapsed_time_ = elapsed_time;
    return *this;
  }

  const std::string& GetId() const noexcept { return id_; }
  const std::string& GetWorkflowId() const noexcept { return workflow_id_; }
  const std::string& GetStatus() const noexcept { return status_; }
  std::map<std::string, std::string> GetInputs() const noexcept {
    return inputs_;
  }
  std::map<std::string, std::string> GetOutputs() const noexcept {
    return outputs_;
  }
  const std::string& GetError() const noexcept { return error_; }
  int GetTotalSteps() const noexcept { return total_steps_; }
  int GetTotalTokens() const noexcept { return total_tokens_; }
  int GetCreatedAt() const noexcept { return created_at_; }
  int GetFinishedAt() const noexcept { return finished_at_; }
  int GetElapsedTime() const noexcept { return elapsed_time_; }

 private:
  std::string id_;
  std::string workflow_id_;
  std::string status_;
  nlohmann::json inputs_;
  nlohmann::json outputs_;
  std::string error_;
  int total_steps_{};
  int total_tokens_{};
  int created_at_{};
  int finished_at_{};
  int elapsed_time_{};
};

class WorkflowTaskStopRequest {
 public:
  WorkflowTaskStopRequest& SetTaskId(const std::string& task_id) {
    task_id_ = task_id;
    return *this;
  }
  WorkflowTaskStopRequest& SetUser(const std::string& user) {
    user_ = user;
    return *this;
  }

  const std::string& GetTaskId() const noexcept { return task_id_; }
  const std::string& GetUser() const noexcept { return user_; }

 private:
  std::string task_id_;
  std::string user_;
};

class WorkflowLogsRequest {
 public:
  WorkflowLogsRequest& SetKeyWord(const std::string& key_word) {
    key_word_ = key_word;
    return *this;
  }
  WorkflowLogsRequest& SetStatus(const std::string& status) {
    status_ = status;
    return *this;
  }
  WorkflowLogsRequest& SetPage(int page) {
    page_ = page;
    return *this;
  }
  WorkflowLogsRequest& SetLimit(int limit) {
    limit_ = limit;
    return *this;
  }
  const std::string& GetKeyWord() const noexcept { return key_word_; }
  const std::string& GetStatus() const noexcept { return status_; }
  int GetPage() const noexcept { return page_; }
  int GetLimit() const noexcept { return limit_; }

 private:
  std::string key_word_;
  std::string status_;
  int page_{};
  int limit_{};
};

class WorkflowLogsResponse {
 public:
  WorkflowLogsResponse& SetPage(int page) {
    page_ = page;
    return *this;
  }
  WorkflowLogsResponse& SetLimit(int limit) {
    limit_ = limit;
    return *this;
  }
  WorkflowLogsResponse& SetTotal(int total) {
    total_ = total;
    return *this;
  }
  WorkflowLogsResponse& SetHasMore(bool has_more) {
    has_more_ = has_more;
    return *this;
  }
  WorkflowLogsResponse& SetData(const std::string& data) {
    data_ = data;
    return *this;
  }
  int GetPage() const noexcept { return page_; }
  int GetLimit() const noexcept { return limit_; }
  int GetTotal() const noexcept { return total_; }
  bool GetHasMore() const noexcept { return has_more_; }
  const std::string& GetData() const noexcept { return data_; }

 private:
  int page_{};
  int limit_{};
  int total_{};
  bool has_more_{};
  std::string data_;
};

}  // namespace amssdk
#endif  //AMSSDK_WORKFLOW_H
