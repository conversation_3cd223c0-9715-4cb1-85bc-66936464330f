#ifndef AMSSDK_TASK_H
#define AMSSDK_TASK_H
#include <string>
namespace amssdk {

class TaskStopRequest {
 public:
  TaskStopRequest() = default;
  TaskStopRequest& SetTaskId(const std::string& task_id) {
    task_id_ = task_id;
    return *this;
  }
  TaskStopRequest& SetUser(const std::string& user) {
    user_ = user;
    return *this;
  }

  const std::string& GetTaskId() const noexcept { return task_id_; }
  const std::string& GetUser() const noexcept { return user_; }

 private:
  std::string task_id_;
  std::string user_;
};

class FeedbackRequest {
 public:
  enum class Rating { kLike, kDislike, kNull };
  FeedbackRequest() = default;

  FeedbackRequest& SetMessageId(const std::string& message_id) {
    message_id_ = message_id;
    return *this;
  }
  FeedbackRequest& SetContent(const std::string& task_id) {
    content_ = task_id;
    return *this;
  }
  FeedbackRequest& SetRating(const Rating rating) {
    rating_ = rating;
    return *this;
  }
  FeedbackRequest& SetUser(const std::string& user) {
    user_ = user;
    return *this;
  }

  Rating GetRating() const noexcept { return rating_; }
  const std::string& GetUser() const noexcept { return user_; }
  const std::string& GetContent() const noexcept { return content_; }
  const std::string& GetMessageId() const noexcept { return message_id_; }

 private:
  std::string message_id_;
  std::string user_;
  std::string content_;
  Rating rating_{Rating::kNull};
};

}  // namespace amssdk
#endif  //AMSSDK_TASK_H
