#ifndef AMSSDK_ENDPOINTS_H
#define AMSSDK_ENDPOINTS_H

#include <string>

namespace amssdk {
namespace endpoints {

constexpr const char* kChatMessages = "/chat-messages";
constexpr const char* kFileUpload = "/files/upload";
constexpr const char* kMessages = "/messages";
constexpr const char* kSuggested = "/suggested";
constexpr const char* kConversations = "/conversations";
constexpr const char* kAudioToText = "/audio-to-text";
constexpr const char* kTextToAudio = "/text-to-audio";
constexpr const char* kInfo = "/info";
constexpr const char* kParameters = "/parameters";
constexpr const char* kMeta = "/meta";
constexpr const char* kFeedbacks = "/feedbacks";
constexpr const char* kUsers = "/users";
constexpr const char* kStop = "/stop";
constexpr const char* kCompletionMessages = "/completion-messages";

constexpr const char* kWorkflows = "/workflows";
constexpr const char* kWorkflowsRun = "/workflows/run";
constexpr const char* kWorkflowTasks = "/workflows/tasks";
constexpr const char* kWorkflowLogs = "/workflows/logs";

// Knowledge base endpoints
constexpr const char* kDatasets = "/datasets";
constexpr const char* kDocumentCreateByText = "/document/create-by-text";
constexpr const char* kDocumentCreateByFile = "/document/create-by-file";
constexpr const char* kDocuments = "/documents";
constexpr const char* kUpdateByText = "/update-by-text";
constexpr const char* kUpdateByFile = "/update-by-file";
constexpr const char* kIndexingStatus = "/indexing-status";
constexpr const char* kSegments = "/segments";
constexpr const char* kRetrieve = "/retrieve";

inline std::string WorkflowLogs(
    const std::map<std::string, std::string>& query) {
  std::string workflow_logs = std::string(kWorkflowLogs) + "?";
  for (const auto& pair : query) {
    workflow_logs += pair.first + "=" + pair.second;
    workflow_logs += "&";
  }
  // remove last '&'
  workflow_logs.pop_back();

  return workflow_logs;
}

inline std::string WorkflowTaskStop(const std::string& task_id) {
  return std::string(kWorkflowTasks) + "/" + task_id + kStop;
}

inline std::string WorkflowRunInfo(const std::string& workflow_id) {
  return std::string(kWorkflowsRun) + "/" + workflow_id;
}

inline std::string TaskStop(const std::string& task_id) {
  return std::string(kChatMessages) + "/" + task_id + kStop;
}

inline std::string TaskFeedback(const std::string& message_id) {
  return std::string(kChatMessages) + "/" + message_id + kFeedbacks;
}

inline std::string Suggested(const std::string& message_id,
                             const std::string& user) {
  return std::string(kMessages) + "/" + message_id + kSuggested +
         "?user=" + user;
}

inline std::string Messages(const std::map<std::string, std::string>& query) {
  std::string endpoint = std::string(kMessages) + "?";
  for (const auto& pair : query) {
    endpoint += pair.first + "=" + pair.second + "&";
  }
  // remove last '&'
  endpoint.pop_back();
  return endpoint;
}

inline std::string Conversations(
    const std::map<std::string, std::string>& query) {
  std::string endpoint = std::string(kConversations) + "?";
  for (const auto& pair : query) {
    endpoint += pair.first + "=" + pair.second + "&";
  }
  // remove last '&'
  endpoint.pop_back();
  return endpoint;
}

inline std::string DeleteConversation(const std::string& conversation_id) {
  return std::string(kConversations) + "/" + conversation_id;
}
inline std::string RenameConversation(const std::string& conversation_id) {
  return std::string(kConversations) + "/" + conversation_id + "/name";
}
inline std::string AppMeta(const std::string& app_id) {
  return std::string(kMeta) + "?user=" + app_id;
}

// Knowledge base endpoint functions
inline std::string CreateDocumentByText(const std::string& dataset_id) {
  return std::string(kDatasets) + "/" + dataset_id + kDocumentCreateByText;
}

inline std::string CreateDocumentByFile(const std::string& dataset_id) {
  return std::string(kDatasets) + "/" + dataset_id + kDocumentCreateByFile;
}

inline std::string ListDatasets(
    const std::map<std::string, std::string>& query) {
  std::string endpoint = std::string(kDatasets) + "?";
  for (const auto& pair : query) {
    endpoint += pair.first + "=" + pair.second + "&";
  }
  // remove last '&'
  endpoint.pop_back();
  return endpoint;
}

inline std::string UpdateDocumentByText(const std::string& dataset_id,
                                        const std::string& document_id) {
  return std::string(kDatasets) + "/" + dataset_id + kDocuments + "/" +
         document_id + kUpdateByText;
}

inline std::string UpdateDocumentByFile(const std::string& dataset_id,
                                        const std::string& document_id) {
  return std::string(kDatasets) + "/" + dataset_id + kDocuments + "/" +
         document_id + kUpdateByFile;
}

inline std::string GetIndexingStatus(const std::string& dataset_id,
                                     const std::string& batch) {
  return std::string(kDatasets) + "/" + dataset_id + kDocuments + "/" + batch +
         kIndexingStatus;
}

inline std::string DeleteDocument(const std::string& dataset_id,
                                  const std::string& document_id) {
  return std::string(kDatasets) + "/" + dataset_id + kDocuments + "/" +
         document_id;
}

inline std::string ListDocuments(const std::string& dataset_id) {
  return std::string(kDatasets) + "/" + dataset_id + kDocuments;
}

inline std::string DeleteDataset(const std::string& dataset_id) {
  return std::string(kDatasets) + "/" + dataset_id;
}

inline std::string CreateSegment(const std::string& dataset_id,
                                 const std::string& document_id) {
  return std::string(kDatasets) + "/" + dataset_id + kDocuments + "/" +
         document_id + kSegments;
}

inline std::string ListSegments(const std::string& dataset_id,
                                const std::string& document_id) {
  return std::string(kDatasets) + "/" + dataset_id + kDocuments + "/" +
         document_id + kSegments;
}

inline std::string DeleteSegment(const std::string& dataset_id,
                                 const std::string& document_id,
                                 const std::string& segment_id) {
  return std::string(kDatasets) + "/" + dataset_id + kDocuments + "/" +
         document_id + kSegments + "/" + segment_id;
}

inline std::string UpdateSegment(const std::string& dataset_id,
                                 const std::string& document_id,
                                 const std::string& segment_id) {
  return std::string(kDatasets) + "/" + dataset_id + kDocuments + "/" +
         document_id + kSegments + "/" + segment_id;
}

inline std::string RetrieveDataset(const std::string& dataset_id) {
  return std::string(kDatasets) + "/" + dataset_id + kRetrieve;
}

}  // namespace endpoints
}  // namespace amssdk

#endif  // AMSSDK_ENDPOINTS_H
