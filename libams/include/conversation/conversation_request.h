
#ifndef AMSSDK_CONVERSATION_REQUEST_H
#define AMSSDK_CONVERSATION_REQUEST_H
#include <string>

namespace amssdk {
class SuggestedRequest {
 public:
  SuggestedRequest& SetMessageId(const std::string& message_id) {
    message_id_ = message_id;
    return *this;
  }
  SuggestedRequest& SetUser(const std::string& user) {
    user_ = user;
    return *this;
  }
  const std::string& GetMessageId() const noexcept { return message_id_; }
  const std::string& GetUser() const noexcept { return user_; }

 private:
  std::string message_id_;
  std::string user_;
};

class MessagesRequest {
 public:
  MessagesRequest& SetConversationId(const std::string& conversation_id) {
    conversation_id_ = conversation_id;
    return *this;
  }
  MessagesRequest& SetUser(const std::string& user) {
    user_ = user;
    return *this;
  }
  MessagesRequest& SetFirstId(const std::string& first_id) {
    first_id_ = first_id;
    return *this;
  }
  MessagesRequest& SetLimit(int limit) {
    limit_ = limit;
    return *this;
  }
  const std::string& GetConversationId() const noexcept {
    return conversation_id_;
  }
  const std::string& GetUser() const noexcept { return user_; }
  const std::string& GetFirstId() const noexcept { return first_id_; }
  int GetLimit() const noexcept { return limit_; }

 private:
  std::string conversation_id_;
  std::string user_;
  std::string first_id_;
  int limit_{};
};

class ConversationRequest {
 public:
  ConversationRequest& SetUser(const std::string& user) {
    user_ = user;
    return *this;
  }
  ConversationRequest& SetLastId(const std::string& last_id) {
    last_id_ = last_id;
    return *this;
  }
  ConversationRequest& SetLimit(const std::string& limit) {
    limit_ = limit;
    return *this;
  }
  ConversationRequest& SetSortRule(SortRule sort_rule) {
    sort_rule_ = sort_rule;
    return *this;
  }
  const std::string& GetUser() const noexcept { return user_; }
  const std::string& GetLastId() const noexcept { return last_id_; }
  const std::string& GetLimit() const noexcept { return limit_; }
  SortRule GetSortRule() const noexcept { return sort_rule_; }

 private:
  std::string user_;
  std::string last_id_;
  std::string limit_;
  SortRule sort_rule_{SortRule::kUpdatedAt};
};

class DeleteConversationRequest {
 public:
  DeleteConversationRequest& SetConversationId(
      const std::string& conversation_id) {
    conversation_id_ = conversation_id;
    return *this;
  }
  DeleteConversationRequest& SetUser(const std::string& user) {
    user_ = user;
    return *this;
  }
  const std::string& GetConversationId() const noexcept {
    return conversation_id_;
  }
  const std::string& GetUser() const noexcept { return user_; }

 private:
  std::string conversation_id_;
  std::string user_;
};

class RenameConversationRequest {
 public:
  RenameConversationRequest& SetName(const std::string& name) {
    name_ = name;
    return *this;
  }
  RenameConversationRequest& SetUser(const std::string& user) {
    user_ = user;
    return *this;
  }
  RenameConversationRequest& SetConversationId(
      const std::string& conversation_id) {
    conversation_id_ = conversation_id;
    return *this;
  }

  RenameConversationRequest& SetAutoGenerateName(bool auto_generate) {
    auto_generate_name_ = auto_generate;
    return *this;
  }

  const std::string& GetName() const noexcept { return name_; }
  const std::string& GetUser() const noexcept { return user_; }
  const std::string& GetConversationId() const noexcept {
    return conversation_id_;
  }
  bool GetAutoGenerateName() const noexcept { return auto_generate_name_; }

 private:
  std::string name_;
  std::string user_;
  std::string conversation_id_;
  bool auto_generate_name_ = false;
};

}  // namespace amssdk
#endif  //AMSSDK_CONVERSATION_REQUEST_H
