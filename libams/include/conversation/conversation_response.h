#ifndef AMSSDK_CONVERSATION_RESPONSE_H
#define AMSSDK_CONVERSATION_RESPONSE_H
#include <string>

namespace amssdk {

class ConversationResponse {
 public:
  int limit{0};
  bool has_more{false};
  std::string data;
};

class SuggestedResponse {
 public:
  SimpleResponse::ResultType result;
  std::string data;
};

class MessagesResponse {
 public:
  int limit{0};
  bool has_more{false};
  std::string data;
};

class DeleteConversationResponse : public SimpleResponse {};

class RenameConversationResponse {
 public:
  std::string id;
  std::string name;
  std::string inputs;
  std::string status;
  std::string introduction;
  int created_at;
  int updated_at;
};
}  // namespace amssdk

#endif  //AMSSDK_CONVERSATION_RESPONSE_H
