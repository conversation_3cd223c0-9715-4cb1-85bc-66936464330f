#ifndef AMSSDK_FILE_H
#define AMSSDK_FILE_H
#include <nlohmann/json.hpp>
#include <string>
#include "include/common/common.h"

namespace amssdk {

class FileRequest {
 public:
  explicit FileRequest() {}
  FileRequest(const std::string& file_path, const std::string& user,
              FileAttachment::FileType file_type)
      : file_path_(file_path), user_(user), file_type_(file_type) {}

  FileRequest& SetFilePath(const std::string& file_path) {
    file_path_ = file_path;
    return *this;
  }
  FileRequest& SetUser(const std::string& user) {
    user_ = user;
    return *this;
  }
  FileRequest& SetFileType(FileAttachment::FileType file_type) {
    file_type_ = file_type;
    return *this;
  }

  const std::string& GetFilePath() const noexcept { return file_path_; }
  const std::string& GetUser() const noexcept { return user_; }
  FileAttachment::FileType GetFileType() const noexcept { return file_type_; }

 private:
  std::string file_path_;
  std::string user_;
  FileAttachment::FileType file_type_{};
};

class FileResponse {
 public:
  std::string id_;
  std::string name_;
  size_t size_;
  FileAttachment::FileType file_type_;
  std::string extension_;
  std::string mime_type_;
  std::string created_by_;
  int created_at_;
};

}  // namespace amssdk

#endif  //AMSSDK_FILE_H
