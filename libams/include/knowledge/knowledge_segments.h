#ifndef AMSSDK_KNOWLEDGE_SEGMENTS_H
#define AMSSDK_KNOWLEDGE_SEGMENTS_H

#include "knowledge.h"

namespace amssdk {

// ===== 新增分段 =====
class CreateSegmentRequest {
 public:
  CreateSegmentRequest() = default;

  CreateSegmentRequest& SetDatasetId(const std::string& dataset_id) {
    dataset_id_ = dataset_id;
    return *this;
  }
  CreateSegmentRequest& SetDocumentId(const std::string& document_id) {
    document_id_ = document_id;
    return *this;
  }
  CreateSegmentRequest& SetSegments(const std::vector<SegmentInfo>& segments) {
    segments_ = segments;
    return *this;
  }

  const std::string& GetDatasetId() const noexcept { return dataset_id_; }
  const std::string& GetDocumentId() const noexcept { return document_id_; }
  const std::vector<SegmentInfo>& GetSegments() const noexcept {
    return segments_;
  }

 private:
  std::string dataset_id_;
  std::string document_id_;
  std::vector<SegmentInfo> segments_;
};

class CreateSegmentResponse {
 public:
  void SetData(const std::string& data) { data_ = data; }
  void SetDocForm(const std::string& doc_form) { doc_form_ = doc_form; }

  std::string GetData() const noexcept { return data_; }
  const std::string& GetDocForm() const noexcept { return doc_form_; }

 private:
  std::string data_;
  std::string doc_form_;
};

// ===== 查询文档分段 =====
class ListSegmentsRequest {
 public:
  ListSegmentsRequest() = default;

  ListSegmentsRequest& SetDatasetId(const std::string& dataset_id) {
    dataset_id_ = dataset_id;
    return *this;
  }
  ListSegmentsRequest& SetDocumentId(const std::string& document_id) {
    document_id_ = document_id;
    return *this;
  }
  ListSegmentsRequest& SetKeyword(const std::string& keyword) {
    keyword_ = keyword;
    return *this;
  }
  ListSegmentsRequest& SetStatus(const std::string& status) {
    status_ = status;
    return *this;
  }

  const std::string& GetDatasetId() const noexcept { return dataset_id_; }
  const std::string& GetDocumentId() const noexcept { return document_id_; }
  const std::string& GetKeyword() const noexcept { return keyword_; }
  const std::string& GetStatus() const noexcept { return status_; }

 private:
  std::string dataset_id_;
  std::string document_id_;
  std::string keyword_;
  std::string status_;
};

class ListSegmentsResponse {
 public:
  void SetData(const std::string& data) { data_ = data; }
  void SetDocForm(const std::string& doc_form) { doc_form_ = doc_form; }

  const std::string& GetData() const noexcept { return data_; }
  const std::string& GetDocForm() const noexcept { return doc_form_; }

 private:
  std::string data_;
  std::string doc_form_;
};

// ===== 删除文档分段 =====
class DeleteSegmentRequest {
 public:
  DeleteSegmentRequest() = default;

  DeleteSegmentRequest& SetDatasetId(const std::string& dataset_id) {
    dataset_id_ = dataset_id;
    return *this;
  }
  DeleteSegmentRequest& SetDocumentId(const std::string& document_id) {
    document_id_ = document_id;
    return *this;
  }
  DeleteSegmentRequest& SetSegmentId(const std::string& segment_id) {
    segment_id_ = segment_id;
    return *this;
  }

  const std::string& GetDatasetId() const noexcept { return dataset_id_; }
  const std::string& GetDocumentId() const noexcept { return document_id_; }
  const std::string& GetSegmentId() const noexcept { return segment_id_; }

 private:
  std::string dataset_id_;
  std::string document_id_;
  std::string segment_id_;
};

class DeleteSegmentResponse {
 public:
  void SetResult(const std::string& result) { result_ = result; }
  const std::string& GetResult() const noexcept { return result_; }

 private:
  std::string result_;
};

// ===== 更新文档分段 =====
class UpdateSegmentRequest {
 public:
  UpdateSegmentRequest() = default;

  UpdateSegmentRequest& SetDatasetId(const std::string& dataset_id) {
    dataset_id_ = dataset_id;
    return *this;
  }
  UpdateSegmentRequest& SetDocumentId(const std::string& document_id) {
    document_id_ = document_id;
    return *this;
  }
  UpdateSegmentRequest& SetSegmentId(const std::string& segment_id) {
    segment_id_ = segment_id;
    return *this;
  }
  UpdateSegmentRequest& SetSegment(const SegmentInfo& segment) {
    segment_ = segment;
    return *this;
  }

  const std::string& GetDatasetId() const noexcept { return dataset_id_; }
  const std::string& GetDocumentId() const noexcept { return document_id_; }
  const std::string& GetSegmentId() const noexcept { return segment_id_; }
  const SegmentInfo& GetSegment() const noexcept { return segment_; }

 private:
  std::string dataset_id_;
  std::string document_id_;
  std::string segment_id_;
  SegmentInfo segment_;
};

class UpdateSegmentResponse {
 public:
  void SetData(const std::vector<SegmentInfo>& data) { data_ = data; }
  void SetDocForm(const std::string& doc_form) { doc_form_ = doc_form; }

  const std::vector<SegmentInfo>& GetData() const noexcept { return data_; }
  const std::string& GetDocForm() const noexcept { return doc_form_; }

 private:
  std::vector<SegmentInfo> data_;
  std::string doc_form_;
};
struct RerankingMode {
  std::string reranking_model_name;
  std::string reranking_provider_name;
};

struct RetrievalModel {
  SearchMethod search_method;
  bool reranking_enable = false;
  RerankingMode reranking_mode;
  double weights = 0.0;
  int top_k = 1;
  bool score_threshold_enabled = false;
  double score_threshold = 0.0;
};

struct RetrievalRecord {
  SegmentInfo segment;
  double score = 0.0;
  nlohmann::json tsne_position;
};

class RetrieveDatasetRequest {
 public:
  RetrieveDatasetRequest() = default;

  RetrieveDatasetRequest& SetDatasetId(const std::string& dataset_id) {
    dataset_id_ = dataset_id;
    return *this;
  }
  RetrieveDatasetRequest& SetQuery(const std::string& query) {
    query_ = query;
    return *this;
  }
  RetrieveDatasetRequest& SetRetrievalModel(const RetrievalModel& model) {
    retrieval_model_ = model;
    return *this;
  }

  const std::string& GetDatasetId() const noexcept { return dataset_id_; }
  const std::string& GetQuery() const noexcept { return query_; }
  const RetrievalModel& GetRetrievalModel() const noexcept {
    return retrieval_model_;
  }

 private:
  std::string dataset_id_;
  std::string query_;
  RetrievalModel retrieval_model_;
};

class RetrieveDatasetResponse {
 public:
  void SetQuery(const std::string& query) { query_ = query; }
  void SetRecords(const std::string& records) { records_ = records; }
  const std::string& GetQuery() const noexcept { return query_; }
  const std::string& GetRecords() const noexcept { return records_; }

 private:
  std::string query_;
  std::string records_;
};

}  // namespace amssdk

#endif  // AMSSDK_KNOWLEDGE_SEGMENTS_H
