#ifndef AMSSDK_KNOWLEDGE_H
#define AMSSDK_KNOWLEDGE_H

#include <experimental/filesystem>
#include <nlohmann/json.hpp>
#include <string>
#include <vector>
#include "include/common/common.h"

namespace amssdk {

struct PreProcessingRule {

  RuleType type;
  std::string id;
  bool enabled = false;
};
struct SegmentationRule {
  std::string separator = R"(\n)";
  int max_tokens = 1000;
};
struct ProcessRule {
  ProcessRuleMode mode = ProcessRuleMode::AUTOMATIC;
  std::vector<PreProcessingRule> pre_processing_rules;
  SegmentationRule segmentation;
};

class CreateDocumentByTextRequest {
 public:
  CreateDocumentByTextRequest() = default;
  CreateDocumentByTextRequest& SetDatasetId(const std::string& dataset_id) {
    dataset_id_ = dataset_id;
    return *this;
  }
  CreateDocumentByTextRequest& SetName(const std::string& name) {
    name_ = name;
    return *this;
  }
  CreateDocumentByTextRequest& SetText(const std::string& text) {
    text_ = text;
    return *this;
  }
  CreateDocumentByTextRequest& SetIndexingTechnique(
      const IndexingTechnique technique) {
    indexing_technique_ = technique;
    return *this;
  }
  CreateDocumentByTextRequest& SetProcessRule(const ProcessRule& rule) {
    process_rule_ = rule;
    return *this;
  }
  // Getters
  const std::string& GetDatasetId() const noexcept { return dataset_id_; }
  const std::string& GetName() const noexcept { return name_; }
  const std::string& GetText() const noexcept { return text_; }
  IndexingTechnique GetIndexingTechnique() const noexcept {
    return indexing_technique_;
  }
  const ProcessRule& GetProcessRule() const noexcept { return process_rule_; }

 private:
  std::string dataset_id_;
  std::string name_;
  std::string text_;
  IndexingTechnique indexing_technique_ = IndexingTechnique::kHighQuality;
  ProcessRule process_rule_;
};

struct DataSourceInfo {
  std::string upload_file_id;
};

// 知识库信息
struct DatasetInfo {
  std::string id;
  std::string name;
  std::string description;
  std::string provider;
  std::string permission;
  std::string data_source_type;
  std::string indexing_technique;
  int app_count = 0;
  int document_count = 0;
  int word_count = 0;
  std::string created_by;
  long created_at = 0;
  std::string updated_by;
  long updated_at = 0;
  std::string embedding_model;
  std::string embedding_model_provider;
  bool embedding_available = false;
};

struct SegmentInfo {
  std::string content;
  std::string answer;
  std::vector<std::string> keywords;
};

// 索引状态信息
struct IndexingStatusInfo {
  std::string id;
  std::string indexing_status;
  double processing_started_at = 0.0;
  double parsing_completed_at = 0.0;
  double cleaning_completed_at = 0.0;
  double splitting_completed_at = 0.0;
  double completed_at = 0.0;
  double paused_at = 0.0;
  std::string error;
  double stopped_at = 0.0;
  int completed_segments = 0;
  int total_segments = 0;
};

struct DocumentInfo {
  std::string id;
  int position = 0;
  std::string data_source_type;
  DataSourceInfo data_source_info;
  std::string dataset_process_rule_id;
  std::string name;
  std::string created_from;
  std::string created_by;
  long created_at = 0;
  int tokens = 0;
  std::string indexing_status;
  std::string error;
  bool enabled = true;
  long disabled_at = 0;
  std::string disabled_by;
  bool archived = false;
  std::string display_status;
  int word_count = 0;
  int hit_count = 0;
  std::string doc_form;
};

class CreateDocumentResponse {
 public:
  CreateDocumentResponse() = default;

  // Setters
  void SetDocument(const std::string& document) { document_json_ = document; }
  void SetBatch(const std::string& batch) { batch_ = batch; }

  // Getters
  const std::string& GetDocument() const noexcept { return document_json_; }
  const std::string& GetBatch() const noexcept { return batch_; }

 private:
  std::string document_json_;
  std::string batch_;
};

class CreateDocumentByFileRequest {
 public:
  CreateDocumentByFileRequest() = default;

  CreateDocumentByFileRequest& SetDatasetId(const std::string& dataset_id) {
    dataset_id_ = dataset_id;
    return *this;
  }
  CreateDocumentByFileRequest& SetFile(
      const std::experimental::filesystem::path& file) {
    file_ = file;
    return *this;
  }
  CreateDocumentByFileRequest& SetIndexingTechnique(
      const IndexingTechnique technique) {
    indexing_technique_ = technique;
    return *this;
  }
  CreateDocumentByFileRequest& SetProcessRule(const ProcessRule& rule) {
    process_rule_ = rule;
    return *this;
  }
  CreateDocumentByFileRequest& SetOriginalDocumentId(const std::string& id) {
    original_document_id_ = id;
    return *this;
  }

  const std::string& GetDatasetId() const noexcept { return dataset_id_; }
  const std::experimental::filesystem::path& GetFile() const noexcept {
    return file_;
  }
  IndexingTechnique GetIndexingTechnique() const noexcept {
    return indexing_technique_;
  }
  const ProcessRule& GetProcessRule() const noexcept { return process_rule_; }

  const std::string& GetOriginalDocumentId() const noexcept {
    return original_document_id_;
  }

 private:
  std::string dataset_id_;
  std::experimental::filesystem::path file_;
  IndexingTechnique indexing_technique_ = IndexingTechnique::kHighQuality;
  ProcessRule process_rule_;
  std::string original_document_id_;
};

class CreateDatasetRequest {
 public:
  CreateDatasetRequest() = default;

  CreateDatasetRequest& SetName(const std::string& name) {
    name_ = name;
    return *this;
  }
  CreateDatasetRequest& SetDescription(const std::string& description) {
    description_ = description;
    return *this;
  }
  CreateDatasetRequest& SetIndexingTechnique(
      const IndexingTechnique technique) {
    indexing_technique_ = technique;
    return *this;
  }
  CreateDatasetRequest& SetPermission(
      const CreateDatasetPermission permission) {
    permission_ = permission;
    return *this;
  }
  CreateDatasetRequest& SetProvider(const CreateDatasetProvider provider) {
    provider_ = provider;
    return *this;
  }
  CreateDatasetRequest& SetExternalKnowledgeApiId(
      const std::string& external_knowledge_api_id) {
    external_knowledge_api_id_ = external_knowledge_api_id;
    return *this;
  }
  CreateDatasetRequest& SetExternalKnowledgeId(
      const std::string& external_knowledge_id) {
    external_knowledge_id_ = external_knowledge_id;
    return *this;
  }

  const std::string& GetName() const noexcept { return name_; }
  const std::string& GetDescription() const noexcept { return description_; }
  IndexingTechnique GetIndexingTechnique() const noexcept {
    return indexing_technique_;
  }
  CreateDatasetPermission GetPermission() const noexcept { return permission_; }
  CreateDatasetProvider GetProvider() const noexcept { return provider_; }
  const std::string& GetExternalKnowledgeApiId() const noexcept {
    return external_knowledge_api_id_;
  }
  const std::string& GetExternalKnowledgeId() const noexcept {
    return external_knowledge_id_;
  }

 private:
  std::string name_;
  std::string description_;
  IndexingTechnique indexing_technique_ = IndexingTechnique::kHighQuality;
  CreateDatasetPermission permission_ = CreateDatasetPermission::kOnlyMe;
  CreateDatasetProvider provider_ = CreateDatasetProvider::kVendor;
  std::string external_knowledge_api_id_;
  std::string external_knowledge_id_;
};

class CreateDatasetResponse {
 public:
  void SetDataset(const DatasetInfo& dataset) { dataset_ = dataset; }
  const DatasetInfo& GetDataset() const noexcept { return dataset_; }

 private:
  DatasetInfo dataset_;
};

class ListDatasetsRequest {
 public:
  ListDatasetsRequest& SetPage(int page) {
    page_ = page;
    return *this;
  }
  ListDatasetsRequest& SetLimit(int limit) {
    limit_ = limit;
    return *this;
  }

  int GetPage() const noexcept { return page_; }
  int GetLimit() const noexcept { return limit_; }

 private:
  int page_ = 1;
  int limit_ = 20;
};

class ListDatasetsResponse {
 public:
  void SetData(const std::string& data) { data_ = data; }
  void SetHasMore(bool has_more) { has_more_ = has_more; }
  void SetLimit(int limit) { limit_ = limit; }
  void SetTotal(int total) { total_ = total; }
  void SetPage(int page) { page_ = page; }

  std::string GetData() const noexcept { return data_; }
  bool GetHasMore() const noexcept { return has_more_; }
  int GetLimit() const noexcept { return limit_; }
  int GetTotal() const noexcept { return total_; }
  int GetPage() const noexcept { return page_; }

 private:
  std::string data_;
  bool has_more_ = false;
  int limit_ = 20;
  int total_ = 0;
  int page_ = 1;
};

class DeleteDatasetRequest {
 public:
  DeleteDatasetRequest& SetDatasetId(const std::string& dataset_id) {
    dataset_id_ = dataset_id;
    return *this;
  }
  const std::string& GetDatasetId() const noexcept { return dataset_id_; }

 private:
  std::string dataset_id_;
};

class UpdateDocumentByTextRequest {
 public:
  UpdateDocumentByTextRequest() = default;

  UpdateDocumentByTextRequest& SetDatasetId(const std::string& dataset_id) {
    dataset_id_ = dataset_id;
    return *this;
  }
  UpdateDocumentByTextRequest& SetDocumentId(const std::string& document_id) {
    document_id_ = document_id;
    return *this;
  }
  UpdateDocumentByTextRequest& SetName(const std::string& name) {
    name_ = name;
    return *this;
  }
  UpdateDocumentByTextRequest& SetText(const std::string& text) {
    text_ = text;
    return *this;
  }
  UpdateDocumentByTextRequest& SetProcessRule(const ProcessRule& rule) {
    process_rule_ = rule;
    return *this;
  }

  const std::string& GetDatasetId() const noexcept { return dataset_id_; }
  const std::string& GetDocumentId() const noexcept { return document_id_; }
  const std::string& GetName() const noexcept { return name_; }
  const std::string& GetText() const noexcept { return text_; }
  const ProcessRule& GetProcessRule() const noexcept { return process_rule_; }

 private:
  std::string dataset_id_;
  std::string document_id_;
  std::string name_;
  std::string text_;
  ProcessRule process_rule_;
};

class UpdateDocumentByFileRequest {
 public:
  UpdateDocumentByFileRequest() = default;

  UpdateDocumentByFileRequest& SetDatasetId(const std::string& dataset_id) {
    dataset_id_ = dataset_id;
    return *this;
  }
  UpdateDocumentByFileRequest& SetDocumentId(const std::string& document_id) {
    document_id_ = document_id;
    return *this;
  }
  UpdateDocumentByFileRequest& SetName(const std::string& name) {
    name_ = name;
    return *this;
  }
  UpdateDocumentByFileRequest& SetFile(
      const std::experimental::filesystem::path& file) {
    file_ = file;
    return *this;
  }
  UpdateDocumentByFileRequest& SetProcessRule(const ProcessRule& rule) {
    process_rule_ = rule;
    return *this;
  }
  UpdateDocumentByFileRequest& SetIndexingTechnique(
      const IndexingTechnique technique) {
    indexing_technique_ = technique;
    return *this;
  }

  const std::string& GetDatasetId() const noexcept { return dataset_id_; }
  const std::string& GetDocumentId() const noexcept { return document_id_; }
  const std::string& GetName() const noexcept { return name_; }
  const std::experimental::filesystem::path& GetFile() const noexcept {
    return file_;
  }
  const ProcessRule& GetProcessRule() const noexcept { return process_rule_; }
  IndexingTechnique GetIndexingTechnique() const noexcept {
    return indexing_technique_;
  }

 private:
  std::string dataset_id_;
  std::string document_id_;
  std::string name_;
  std::experimental::filesystem::path file_;
  IndexingTechnique indexing_technique_ = IndexingTechnique::kHighQuality;
  ProcessRule process_rule_;
};

class GetIndexingStatusRequest {
 public:
  GetIndexingStatusRequest() = default;

  GetIndexingStatusRequest& SetDatasetId(const std::string& dataset_id) {
    dataset_id_ = dataset_id;
    return *this;
  }
  GetIndexingStatusRequest& SetBatch(const std::string& batch) {
    batch_ = batch;
    return *this;
  }

  const std::string& GetDatasetId() const noexcept { return dataset_id_; }
  const std::string& GetBatch() const noexcept { return batch_; }

 private:
  std::string dataset_id_;
  std::string batch_;
};

class GetIndexingStatusResponse {
 public:
  void SetData(const std::string& data) { data_ = data; }
  std::string GetData() const noexcept { return data_; }

 private:
  std::string data_;
};

class DeleteDocumentRequest {
 public:
  DeleteDocumentRequest() = default;

  DeleteDocumentRequest& SetDatasetId(const std::string& dataset_id) {
    dataset_id_ = dataset_id;
    return *this;
  }
  DeleteDocumentRequest& SetDocumentId(const std::string& document_id) {
    document_id_ = document_id;
    return *this;
  }

  const std::string& GetDatasetId() const noexcept { return dataset_id_; }
  const std::string& GetDocumentId() const noexcept { return document_id_; }

 private:
  std::string dataset_id_;
  std::string document_id_;
};

class DeleteDocumentResponse {
 public:
  void SetResult(const std::string& result) { result_ = result; }
  const std::string& GetResult() const noexcept { return result_; }

 private:
  std::string result_;
};

// ===== 文档列表 =====
class ListDocumentsRequest {
 public:
  ListDocumentsRequest() = default;

  ListDocumentsRequest& SetDatasetId(const std::string& dataset_id) {
    dataset_id_ = dataset_id;
    return *this;
  }
  ListDocumentsRequest& SetKeyword(const std::string& keyword) {
    keyword_ = keyword;
    return *this;
  }
  ListDocumentsRequest& SetPage(int page) {
    page_ = page;
    return *this;
  }
  ListDocumentsRequest& SetLimit(int limit) {
    limit_ = limit;
    return *this;
  }

  const std::string& GetDatasetId() const noexcept { return dataset_id_; }
  const std::string& GetKeyword() const noexcept { return keyword_; }
  int GetPage() const noexcept { return page_; }
  int GetLimit() const noexcept { return limit_; }

 private:
  std::string dataset_id_;
  std::string keyword_;
  int page_ = 1;
  int limit_ = 20;
};

class ListDocumentsResponse {
 public:
  void SetData(const std::vector<DocumentInfo>& data) { data_ = data; }
  void SetHasMore(bool has_more) { has_more_ = has_more; }
  void SetLimit(int limit) { limit_ = limit; }
  void SetTotal(int total) { total_ = total; }
  void SetPage(int page) { page_ = page; }

  const std::vector<DocumentInfo>& GetData() const noexcept { return data_; }
  bool GetHasMore() const noexcept { return has_more_; }
  int GetLimit() const noexcept { return limit_; }
  int GetTotal() const noexcept { return total_; }
  int GetPage() const noexcept { return page_; }

 private:
  std::vector<DocumentInfo> data_;
  bool has_more_ = false;
  int limit_ = 20;
  int total_ = 0;
  int page_ = 1;
};
}  // namespace amssdk
#endif  // AMSSDK_KNOWLEDGE_H
