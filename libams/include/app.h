#ifndef AMSSDK_APP_H
#define AMSSDK_APP_H
#include <string>
#include <vector>

namespace amssdk {

class AppInfoRequest {
 public:
  AppInfoRequest& SetUser(const std::string& user) {
    user_ = user;
    return *this;
  }

 private:
  std::string user_;
};

class AppInfoResponse {
 public:
  AppInfoResponse& SetName(const std::string& name) {
    name_ = name;
    return *this;
  }
  AppInfoResponse& SetDescription(const std::string& description) {
    description_ = description;
    return *this;
  }
  AppInfoResponse& SetTags(const std::vector<std::string>& tags) {
    tags_ = tags;
    return *this;
  }
  std::string GetName() const noexcept { return name_; }
  std::string GetDescription() const noexcept { return description_; }
  std::vector<std::string> GetTags() const noexcept { return tags_; }

 private:
  std::string name_;
  std::string description_;
  std::vector<std::string> tags_;
};

class AppParamRequest {
 public:
  AppParamRequest& SetUser(const std::string& user) {
    user_ = user;
    return *this;
  }

 private:
  std::string user_;
};

class AppParamResponse {
 public:
  AppParamResponse& SetParamsJson(const std::string& params_json) {
    params_json_ = params_json;
    return *this;
  }
  std::string GetParams() const noexcept { return params_json_; }

 private:
  std::string params_json_;
};

class AppMetaRequest {
 public:
  AppMetaRequest& SetUser(const std::string& user) {
    user_ = user;
    return *this;
  }
  const std::string& GetUser() const noexcept { return user_; }

 private:
  std::string user_;
};

class AppMetaResponse {
 public:
  AppMetaResponse& SetMeta(const std::string& meta) {
    meta_json_ = meta;
    return *this;
  }
  std::string GetMeta() const noexcept { return meta_json_; }

 private:
  std::string meta_json_;
};

}  // namespace amssdk

#endif  //AMSSDK_APP_H
